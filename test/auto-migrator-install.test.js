const AutoMigrator = require('../src/app/AutoMigrator');
const path = require('path');
const fs = require('fs-extra');

describe('AutoMigrator Dependency Installation', () => {
  let testProjectPath;
  let autoMigrator;

  beforeEach(async () => {
    // 创建测试项目目录
    testProjectPath = path.join(__dirname, 'fixtures', 'test-project-install');
    await fs.ensureDir(testProjectPath);

    // 创建基本的 package.json
    const packageJson = {
      name: 'test-vue-project',
      version: '1.0.0',
      scripts: {
        dev: 'echo "Mock dev server starting..."',
        build: 'echo "Mock build completed"'
      },
      dependencies: {
        vue: '^3.0.0'
      }
    };
    await fs.writeJson(path.join(testProjectPath, 'package.json'), packageJson, { spaces: 2 });

    autoMigrator = new AutoMigrator(testProjectPath, null, {
      verbose: true,
      dryRun: true
    });
  });

  afterEach(async () => {
    // 清理资源
    if (autoMigrator) {
      await autoMigrator.cleanup();
    }

    // 清理测试目录
    await fs.remove(testProjectPath);
  });

  describe('installDependencies', () => {
    it('应该在预览模式下成功执行', async () => {
      const result = await autoMigrator.installDependencies();

      expect(result.success).toBe(true);
      expect(result.message).toContain('预览模式');
      expect(result.command).toBe('pnpm install');
    });

    it('应该在跳过步骤时返回跳过状态', async () => {
      autoMigrator.options.skipSteps = ['install'];

      const result = await autoMigrator.installDependencies();

      expect(result.success).toBe(true);
      expect(result.skipped).toBe(true);
    });

    it('应该使用自定义安装命令', async () => {
      autoMigrator.options.buildFixerOptions = {
        installCommand: 'npm ci'
      };

      const result = await autoMigrator.installDependencies();

      expect(result.success).toBe(true);
      expect(result.command).toBe('npm ci');
    });

    it('应该使用默认的 pnpm install 命令', async () => {
      const result = await autoMigrator.installDependencies();

      expect(result.success).toBe(true);
      expect(result.command).toBe('pnpm install');
    });
  });

  describe('migrate workflow with install step', () => {
    it('应该在正确的位置执行依赖安装', async () => {
      // 跳过所有步骤来测试流程
      autoMigrator.options.skipSteps = ['config', 'package', 'sass', 'vue', 'install', 'build', 'devserver', 'validate'];

      const result = await autoMigrator.migrate();

      // 验证依赖安装步骤存在且被跳过
      expect(result.stepResults.dependencyInstall).toBeDefined();
      expect(result.stepResults.dependencyInstall.skipped).toBe(true);

      // 验证步骤顺序
      expect(result.stepResults.vueMigration).toBeDefined();
      expect(result.stepResults.dependencyInstall).toBeDefined();
      expect(result.stepResults.buildFix).toBeDefined();
    });

    it('应该在依赖安装失败时停止流程', async () => {
      // 只跳过前面的步骤，让依赖安装执行
      autoMigrator.options.skipSteps = ['config', 'package', 'sass', 'vue'];
      autoMigrator.options.dryRun = false; // 关闭预览模式来测试实际执行

      autoMigrator.installDependencies = jest.fn().mockRejectedValue(new Error('安装失败'));

      try {
        await autoMigrator.migrate();
        fail('应该抛出异常');
      } catch (error) {
        expect(error.message).toContain('安装失败');
      }

      // 验证后续步骤没有执行
      expect(autoMigrator.stats.stepResults.dependencyInstall).toBeNull();
    });
  });

  describe('step numbering', () => {
    it('应该正确显示步骤编号', async () => {
      // 测试步骤编号是否正确
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      autoMigrator.options.skipSteps = ['config', 'package', 'sass', 'vue', 'install', 'build', 'devserver', 'validate'];

      await autoMigrator.migrate();

      // 检查最终报告中的步骤编号
      const logCalls = consoleSpy.mock.calls.map(call => call.join(' '));
      const stepLines = logCalls.filter(line => line.includes('/8'));

      expect(stepLines.length).toBeGreaterThan(0);

      consoleSpy.mockRestore();
    });
  });

  describe('buildFixerOptions integration', () => {
    it('应该从 buildFixerOptions 中获取安装命令', async () => {
      const customInstallCommand = 'yarn install --frozen-lockfile';

      autoMigrator.options.buildFixerOptions = {
        installCommand: customInstallCommand
      };

      const result = await autoMigrator.installDependencies();

      expect(result.command).toBe(customInstallCommand);
    });

    it('应该处理复杂的安装命令', async () => {
      const complexCommand = 'npm install --legacy-peer-deps --no-audit';

      autoMigrator.options.buildFixerOptions = {
        installCommand: complexCommand
      };

      const result = await autoMigrator.installDependencies();

      expect(result.command).toBe(complexCommand);
    });
  });
});
