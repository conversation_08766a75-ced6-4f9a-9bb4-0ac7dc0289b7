const fs = require('fs-extra');
const path = require('path');
const { expect } = require('@jest/globals');
const VueRouteParser = require('../../../../src/domain/page-validate/router/VueRouteParser');

// Mock AI Service
jest.mock('../../../../src/ai/AiService', () => ({
  AIService: class MockAIService {
    constructor(options = {}) {
      this.options = options;
      this.enabled = options.enabled !== false;
    }

    isEnabled() {
      return this.enabled;
    }

    async callAI(prompt, options = {}) {
      // Mock AI response for route parsing
      if (prompt.includes('路由配置文件')) {
        return JSON.stringify([
          {
            "path": "/dashboard",
            "name": "Dashboard",
            "meta": {}
          },
          {
            "path": "/user/:id",
            "name": "UserDetail",
            "meta": {}
          }
        ]);
      }
      return '[]';
    }
  }
}));

describe('VueRouteParser', () => {
  let testProjectPath;
  let parser;

  beforeEach(async () => {
    testProjectPath = await global.testUtils.createTempDir('vue-route-parser-test-');
    parser = new VueRouteParser(testProjectPath, {
      verbose: false,
      useAI: false // 默认关闭 AI，避免实际调用
    });
  });

  afterEach(async () => {
    await global.testUtils.cleanup();
  });

  describe('Constructor', () => {
    test('should initialize with default options', () => {
      const defaultParser = new VueRouteParser('/test/path');

      expect(defaultParser.projectPath).toBe('/test/path');
      expect(defaultParser.options.verbose).toBe(false);
      expect(defaultParser.options.useAI).toBe(true);
      expect(defaultParser.options.routerPaths).toContain('src/router/index.js');
      expect(defaultParser.routes).toEqual([]);
      expect(defaultParser.routerFiles).toEqual([]);
      expect(defaultParser.parseErrors).toEqual([]);
    });

    test('should merge custom options', () => {
      const customParser = new VueRouteParser('/test/path', {
        verbose: true,
        useAI: false,
        routerPaths: ['custom/router.js']
      });

      expect(customParser.options.verbose).toBe(true);
      expect(customParser.options.useAI).toBe(false);
      expect(customParser.options.routerPaths).toEqual(['custom/router.js']);
    });
  });

  describe('findRouterFiles', () => {
    test('should find existing router files', async () => {
      // 创建测试路由文件
      await fs.ensureDir(path.join(testProjectPath, 'src/router'));
      await fs.writeFile(
        path.join(testProjectPath, 'src/router/index.js'),
        'export default []'
      );

      await parser.findRouterFiles();

      expect(parser.routerFiles).toHaveLength(1);
      expect(parser.routerFiles[0].path).toBe('src/router/index.js');
    });

    test('should find multiple router files', async () => {
      // 创建多个路由文件
      await fs.ensureDir(path.join(testProjectPath, 'src/router'));
      await fs.ensureDir(path.join(testProjectPath, 'src/router/modules'));
      await fs.writeFile(
        path.join(testProjectPath, 'src/router/index.js'),
        'export default []'
      );
      await fs.writeFile(
        path.join(testProjectPath, 'src/router/modules/user.js'),
        'export default []'
      );

      await parser.findRouterFiles();

      expect(parser.routerFiles.length).toBeGreaterThanOrEqual(1);
      const paths = parser.routerFiles.map(f => f.path);
      expect(paths).toContain('src/router/index.js');
    });

    test('should handle missing router directory', async () => {
      await parser.findRouterFiles();
      expect(parser.routerFiles).toEqual([]);
    });
  });

  describe('staticParseRoutes', () => {
    test('should parse simple route array', async () => {
      const routeContent = `
        const routes = [
          {
            path: '/home',
            name: 'Home',
            component: () => import('@/views/Home.vue')
          },
          {
            path: '/about',
            name: 'About',
            component: () => import('@/views/About.vue')
          }
        ];
        export default routes;
      `;

      const routes = await parser.staticParseRoutes(routeContent, 'test.js');

      expect(routes).toHaveLength(2);
      expect(routes[0].path).toBe('/home');
      expect(routes[0].name).toBe('Home');
      expect(routes[1].path).toBe('/about');
      expect(routes[1].name).toBe('About');
    });

    test('should parse Vue 3 createRouter format', async () => {
      const routeContent = `
        import { createRouter, createWebHistory } from 'vue-router';

        const routes = [
          {
            path: '/dashboard',
            name: 'Dashboard',
            component: () => import('@/views/Dashboard.vue')
          }
        ];

        const router = createRouter({
          history: createWebHistory(),
          routes
        });

        export default router;
      `;

      const routes = await parser.staticParseRoutes(routeContent, 'test.js');

      expect(routes).toHaveLength(1);
      expect(routes[0].path).toBe('/dashboard');
      expect(routes[0].name).toBe('Dashboard');
    });

    test('should parse Vue 2 VueRouter format', async () => {
      const routeContent = `
        import VueRouter from 'vue-router';

        const routes = [
          {
            path: '/profile',
            name: 'Profile',
            component: () => import('@/views/Profile.vue')
          }
        ];

        const router = new VueRouter({
          mode: 'history',
          routes
        });

        export default router;
      `;

      const routes = await parser.staticParseRoutes(routeContent, 'test.js');

      expect(routes).toHaveLength(1);
      expect(routes[0].path).toBe('/profile');
      expect(routes[0].name).toBe('Profile');
    });

    test('should parse nested routes', async () => {
      const routeContent = `
        const routes = [
          {
            path: '/admin',
            name: 'Admin',
            component: () => import('@/layouts/AdminLayout.vue'),
            children: [
              {
                path: 'users',
                name: 'AdminUsers',
                component: () => import('@/views/admin/Users.vue')
              },
              {
                path: 'settings',
                name: 'AdminSettings',
                component: () => import('@/views/admin/Settings.vue')
              }
            ]
          }
        ];
      `;

      const routes = await parser.staticParseRoutes(routeContent, 'test.js');

      expect(routes).toHaveLength(1);
      expect(routes[0].path).toBe('/admin');
      expect(routes[0].children).toHaveLength(2);
      expect(routes[0].children[0].path).toBe('users');
      expect(routes[0].children[1].path).toBe('settings');
    });

    test('should parse routes with meta information', async () => {
      const routeContent = `
        const routes = [
          {
            path: '/protected',
            name: 'Protected',
            component: () => import('@/views/Protected.vue'),
            meta: {
              requiresAuth: true,
              title: 'Protected Page'
            }
          }
        ];
      `;

      const routes = await parser.staticParseRoutes(routeContent, 'test.js');

      expect(routes).toHaveLength(1);
      expect(routes[0].meta.requiresAuth).toBe(true);
      expect(routes[0].meta.title).toBe('Protected Page');
    });

    test('should handle invalid JavaScript gracefully', async () => {
      const invalidContent = `
        const routes = [
          {
            path: '/invalid'
            // missing comma and closing bracket
      `;

      const routes = await parser.staticParseRoutes(invalidContent, 'test.js');

      expect(routes).toEqual([]);
    });
  });

  describe('extractRouteFromObject', () => {
    test('should extract basic route properties', () => {
      const routeObject = {
        type: 'ObjectExpression',
        properties: [
          {
            key: { type: 'Identifier', name: 'path' },
            value: { type: 'StringLiteral', value: '/test' }
          },
          {
            key: { type: 'Identifier', name: 'name' },
            value: { type: 'StringLiteral', value: 'Test' }
          }
        ]
      };

      const route = parser.extractRouteFromObject(routeObject);

      expect(route.path).toBe('/test');
      expect(route.name).toBe('Test');
    });

    test('should return null for invalid route object', () => {
      const invalidObject = {
        type: 'ObjectExpression',
        properties: []
      };

      const route = parser.extractRouteFromObject(invalidObject);

      expect(route).toBeNull();
    });
  });

  describe('extractComponentInfo', () => {
    test('should extract dynamic import component', () => {
      const componentNode = {
        type: 'ArrowFunctionExpression',
        body: {
          type: 'CallExpression',
          callee: { type: 'Import' },
          arguments: [{ type: 'StringLiteral', value: '@/views/Home.vue' }]
        }
      };

      const componentInfo = parser.extractComponentInfo(componentNode);

      expect(componentInfo.type).toBe('dynamic');
      expect(componentInfo.source).toBe('@/views/Home.vue');
    });

    test('should extract imported component', () => {
      const componentNode = {
        type: 'Identifier',
        name: 'HomeComponent'
      };

      const componentInfo = parser.extractComponentInfo(componentNode);

      expect(componentInfo.type).toBe('imported');
      expect(componentInfo.name).toBe('HomeComponent');
    });
  });

  describe('parseRoutes - Integration Tests', () => {
    test('should parse complete router file successfully', async () => {
      // 创建完整的路由文件
      await fs.ensureDir(path.join(testProjectPath, 'src/router'));
      const routerContent = `
        import { createRouter, createWebHistory } from 'vue-router';

        const routes = [
          {
            path: '/',
            name: 'Home',
            component: () => import('@/views/Home.vue')
          },
          {
            path: '/about',
            name: 'About',
            component: () => import('@/views/About.vue'),
            meta: {
              title: 'About Us'
            }
          },
          {
            path: '/user/:id',
            name: 'UserProfile',
            component: () => import('@/views/UserProfile.vue'),
            children: [
              {
                path: 'posts',
                name: 'UserPosts',
                component: () => import('@/views/UserPosts.vue')
              }
            ]
          }
        ];

        const router = createRouter({
          history: createWebHistory(),
          routes
        });

        export default router;
      `;

      await fs.writeFile(
        path.join(testProjectPath, 'src/router/index.js'),
        routerContent
      );

      const result = await parser.parseRoutes();

      expect(result.success).toBe(true);
      expect(result.routes.length).toBeGreaterThan(0);
      expect(result.routerFiles).toHaveLength(1);
      expect(result.errors).toEqual([]);
    });

    test('should handle missing router files', async () => {
      const result = await parser.parseRoutes();

      expect(result.success).toBe(false);
      expect(result.routes).toEqual([]);
      expect(result.routerFiles).toEqual([]);
      expect(result.errors).toContain('未找到路由配置文件');
    });

    test('should handle parsing errors gracefully', async () => {
      // 创建有语法错误的路由文件
      await fs.ensureDir(path.join(testProjectPath, 'src/router'));
      await fs.writeFile(
        path.join(testProjectPath, 'src/router/index.js'),
        'invalid javascript syntax {'
      );

      const result = await parser.parseRoutes();

      expect(result.success).toBe(true); // 静态解析失败但不会导致整体失败
      expect(Array.isArray(result.errors)).toBe(true);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  describe('getAllRoutePaths', () => {
    beforeEach(() => {
      // 设置测试路由数据
      parser.routes = [
        {
          path: '/',
          name: 'Home',
          component: { type: 'dynamic', source: '@/views/Home.vue' },
          children: []
        },
        {
          path: '/admin',
          name: 'Admin',
          component: { type: 'dynamic', source: '@/layouts/AdminLayout.vue' },
          children: [
            {
              path: 'users',
              name: 'AdminUsers',
              component: { type: 'dynamic', source: '@/views/admin/Users.vue' },
              children: []
            },
            {
              path: 'settings',
              name: 'AdminSettings',
              component: { type: 'dynamic', source: '@/views/admin/Settings.vue' },
              children: []
            }
          ]
        },
        {
          path: '/user/:id',
          name: 'UserProfile',
          component: { type: 'dynamic', source: '@/views/UserProfile.vue' },
          children: []
        }
      ];
    });

    test('should extract all route paths including nested routes', () => {
      const paths = parser.getAllRoutePaths();

      expect(paths).toEqual(
        expect.arrayContaining([
          expect.objectContaining({ path: '/' }),
          expect.objectContaining({ path: '/admin' }),
          expect.objectContaining({ path: '/admin/users' }),
          expect.objectContaining({ path: '/admin/settings' }),
          expect.objectContaining({ path: '/user/:id' })
        ])
      );
    });

    test('should include route metadata', () => {
      const paths = parser.getAllRoutePaths();
      const homePath = paths.find(p => p.path === '/');

      expect(homePath).toHaveProperty('name', 'Home');
      expect(homePath).toHaveProperty('hasChildren', false);
    });

    test('should mark parent routes with children', () => {
      const paths = parser.getAllRoutePaths();
      const adminPath = paths.find(p => p.path === '/admin');

      expect(adminPath).toHaveProperty('hasChildren', true);
    });
  });

  describe('AI Parsing', () => {
    beforeEach(() => {
      // 启用 AI 功能进行测试
      parser.options.useAI = true;
    });

    test('should use AI when static parsing fails', async () => {
      // 创建无法静态解析的路由文件
      await fs.ensureDir(path.join(testProjectPath, 'src/router'));
      await fs.writeFile(
        path.join(testProjectPath, 'src/router/index.js'),
        'complex dynamic route configuration that cannot be statically parsed'
      );

      const result = await parser.parseRoutes();

      expect(result.success).toBe(true);
      // AI mock 会返回两个路由
      expect(result.routes.length).toBeGreaterThan(0);
    });

    test('should normalize AI parsed routes', () => {
      const aiRoutes = [
        {
          path: '/dashboard',
          name: 'Dashboard',
          meta: { title: 'Dashboard' }
        },
        {
          path: '/user/*',
          name: 'UserWildcard'
        }
      ];

      const normalized = parser.normalizeAIRoutes(aiRoutes);

      expect(normalized).toHaveLength(1); // 通配符路由被过滤
      expect(normalized[0].path).toBe('/dashboard');
      expect(normalized[0].name).toBe('Dashboard');
      expect(normalized[0].meta.title).toBe('Dashboard');
    });
  });

  describe('Component Path Resolution', () => {
    test('should resolve component path from route', () => {
      const componentPath = parser.resolveComponentPath('@/views/Home.vue');
      expect(componentPath).toBe('src/views/Home.vue');
    });

    test('should handle relative paths', () => {
      parser.currentFilePath = 'src/router/index.js';
      const componentPath = parser.resolveComponentPath('./components/Layout.vue');
      expect(componentPath).toContain('Layout.vue');
    });

    test('should add .vue extension if missing', () => {
      const componentPath = parser.resolveComponentPath('@/views/Home');
      expect(componentPath).toBe('src/views/Home.vue');
    });
  });

  describe('Route Component Mapping', () => {
    test('should build route to component mapping', () => {
      const route = {
        path: '/home',
        component: {
          type: 'dynamic',
          source: '@/views/Home.vue'
        },
        children: []
      };

      parser.buildRouteComponentMapping(route);

      const componentPath = parser.getComponentPathByRoute('/home');
      expect(componentPath).toBe('src/views/Home.vue');
    });

    test('should handle nested route mappings', () => {
      const route = {
        path: '/admin',
        component: {
          type: 'dynamic',
          source: '@/layouts/AdminLayout.vue'
        },
        children: [
          {
            path: 'users',
            component: {
              type: 'dynamic',
              source: '@/views/admin/Users.vue'
            },
            children: []
          }
        ]
      };

      parser.buildRouteComponentMapping(route);

      expect(parser.getComponentPathByRoute('/admin')).toBe('src/layouts/AdminLayout.vue');
      expect(parser.getComponentPathByRoute('/admin/users')).toBe('src/views/admin/Users.vue');
    });
  });

  describe('Path Inference', () => {
    test('should infer component paths from route path', () => {
      const paths = parser.inferPathsFromRoute('/user/profile');

      expect(paths).toEqual(
        expect.arrayContaining([
          'src/views/user/profile.vue',
          'src/views/user/profile/index.vue',
          'src/components/user/profile.vue'
        ])
      );
    });

    test('should handle root path inference', () => {
      const paths = parser.inferPathsFromRoute('/');

      expect(paths).toEqual(
        expect.arrayContaining([
          'src/views/dashboard/index.vue',
          'src/views/home/<USER>'
        ])
      );
    });

    test('should infer paths from error messages', () => {
      const errorMessage = 'UserChart component not found';
      const paths = parser.inferPathsFromError(errorMessage);

      expect(paths).toEqual(
        expect.arrayContaining([
          'src/components/UserChart.vue',
          'src/components/Charts/UserChart.vue'
        ])
      );
    });
  });

  describe('Utility Methods', () => {
    test('should normalize paths correctly', () => {
      expect(parser.normalizePath('/admin', 'users')).toBe('/admin/users');
      expect(parser.normalizePath('/admin/', '/users')).toBe('/admin/users');
      expect(parser.normalizePath('/admin', '/users')).toBe('/admin/users');
      expect(parser.normalizePath('', '/users')).toBe('/users');
    });

    test('should get routes and route paths', () => {
      parser.routes = [
        { path: '/test', name: 'Test', children: [] }
      ];

      expect(parser.getRoutes()).toEqual(parser.routes);
      expect(parser.getRoutePaths()).toEqual(parser.getAllRoutePaths());
    });
  });
});
