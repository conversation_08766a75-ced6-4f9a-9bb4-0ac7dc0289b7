const fs = require('fs-extra');
const path = require('path');
const PackageJsonMerger = require('../../../src/features/package-json/PackageJsonMerger');

describe('PackageJsonMerger', () => {
  let tempDir;
  let sourceProjectPath;
  let targetProjectPath;
  let merger;

  beforeEach(async () => {
    // 创建临时目录
    tempDir = path.join(__dirname, '../../temp', `test-${Date.now()}`);
    sourceProjectPath = path.join(tempDir, 'source');
    targetProjectPath = path.join(tempDir, 'target');

    await fs.ensureDir(sourceProjectPath);
    await fs.ensureDir(targetProjectPath);

    // 创建测试用的 package.json 文件
    const sourcePackageJson = {
      name: 'source-project',
      version: '1.0.0',
      dependencies: {
        vue: '^2.6.14',
        'element-ui': '^2.15.13',
        axios: '^0.27.2'
      },
      devDependencies: {
        'vue-template-compiler': '^2.6.14',
        webpack: '^4.46.0'
      }
    };

    const targetPackageJson = {
      name: 'target-project',
      version: '2.0.0',
      dependencies: {
        vue: '^3.4.0',
        'vue-router': '^4.5.0'
      },
      devDependencies: {
        '@vue/cli-service': '^5.0.8'
      }
    };

    await fs.writeJson(path.join(sourceProjectPath, 'package.json'), sourcePackageJson, { spaces: 2 });
    await fs.writeJson(path.join(targetProjectPath, 'package.json'), targetPackageJson, { spaces: 2 });

    merger = new PackageJsonMerger(sourceProjectPath, targetProjectPath, {
      verbose: true,
      dryRun: true
    });
  });

  afterEach(async () => {
    // 清理临时文件
    if (await fs.pathExists(tempDir)) {
      await fs.remove(tempDir);
    }
  });

  describe('addBrowserifyPolyfills', () => {
    it('should add path-browserify and stream-browserify dependencies', async () => {
      const result = await merger.merge();

      expect(result.success).toBe(true);
      expect(result.mergedPackageJson).toBeDefined();
      
      const dependencies = result.mergedPackageJson.dependencies;
      
      // 验证 browserify polyfills 被添加
      expect(dependencies['path-browserify']).toBe('^1.0.1');
      expect(dependencies['stream-browserify']).toBe('^3.0.0');
    });

    it('should not override existing browserify polyfills', async () => {
      // 在目标项目中预先添加不同版本的 polyfill
      const targetPackageJsonPath = path.join(targetProjectPath, 'package.json');
      const targetPackageJson = await fs.readJson(targetPackageJsonPath);
      targetPackageJson.dependencies['path-browserify'] = '^0.9.0';
      await fs.writeJson(targetPackageJsonPath, targetPackageJson, { spaces: 2 });

      const result = await merger.merge();

      expect(result.success).toBe(true);
      
      const dependencies = result.mergedPackageJson.dependencies;
      
      // 验证现有版本被保留
      expect(dependencies['path-browserify']).toBe('^0.9.0');
      // 验证新的 polyfill 仍然被添加
      expect(dependencies['stream-browserify']).toBe('^3.0.0');
    });

    it('should handle missing dependencies section', async () => {
      // 创建一个没有 dependencies 的目标 package.json
      const targetPackageJsonPath = path.join(targetProjectPath, 'package.json');
      const targetPackageJson = {
        name: 'target-project',
        version: '2.0.0',
        devDependencies: {
          '@vue/cli-service': '^5.0.8'
        }
      };
      await fs.writeJson(targetPackageJsonPath, targetPackageJson, { spaces: 2 });

      const result = await merger.merge();

      expect(result.success).toBe(true);
      expect(result.mergedPackageJson.dependencies).toBeDefined();
      expect(result.mergedPackageJson.dependencies['path-browserify']).toBe('^1.0.1');
      expect(result.mergedPackageJson.dependencies['stream-browserify']).toBe('^3.0.0');
    });
  });

  describe('dependency mapping and compatibility', () => {
    it('should map element-ui to element-plus', async () => {
      const result = await merger.merge();

      expect(result.success).toBe(true);
      
      const dependencies = result.mergedPackageJson.dependencies;
      
      // 验证 element-ui 被映射到 element-plus
      expect(dependencies['element-ui']).toBeUndefined();
      expect(dependencies['element-plus']).toBeDefined();
    });

    it('should skip Vue 2 official dependencies', async () => {
      const result = await merger.merge();

      expect(result.success).toBe(true);
      
      const devDependencies = result.mergedPackageJson.devDependencies;
      
      // 验证 vue-template-compiler 被跳过
      expect(devDependencies['vue-template-compiler']).toBeUndefined();
    });

    it('should preserve target project dependencies', async () => {
      const result = await merger.merge();

      expect(result.success).toBe(true);
      
      const dependencies = result.mergedPackageJson.dependencies;
      
      // 验证目标项目的 Vue 3 版本被保留
      expect(dependencies.vue).toBe('^3.4.0');
      expect(dependencies['vue-router']).toBe('^4.5.0');
    });
  });
});
