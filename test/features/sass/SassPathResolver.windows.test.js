const fs = require('fs-extra');
const path = require('path');
const { expect } = require('@jest/globals');
const SassPathResolver = require('../../../src/features/sass/SassPathResolver');

describe('SassPathResolver - Windows Path Compatibility', () => {
  let testProjectPath;
  let resolver;

  beforeEach(async () => {
    testProjectPath = path.join(__dirname, '../fixtures/path-resolver-test');
    await fs.ensureDir(testProjectPath);

    resolver = new SassPathResolver(testProjectPath, {
      verbose: false
    });
  });

  afterEach(async () => {
    if (await fs.pathExists(testProjectPath)) {
      await fs.remove(testProjectPath);
    }
  });

  describe('Path Normalization', () => {
    test('should normalize Windows backslashes to forward slashes', () => {
      const windowsPath = 'src\\styles\\variables.scss';
      const normalized = resolver.normalizePath(windowsPath);
      
      expect(normalized).toBe('src/styles/variables.scss');
    });

    test('should handle mixed path separators', () => {
      const mixedPath = 'src/styles\\components\\button.scss';
      const normalized = resolver.normalizePath(mixedPath);
      
      expect(normalized).toBe('src/styles/components/button.scss');
    });

    test('should handle empty or null paths', () => {
      expect(resolver.normalizePath('')).toBe('');
      expect(resolver.normalizePath(null)).toBe(null);
      expect(resolver.normalizePath(undefined)).toBe(undefined);
    });

    test('should preserve forward slashes', () => {
      const unixPath = 'src/styles/variables.scss';
      const normalized = resolver.normalizePath(unixPath);
      
      expect(normalized).toBe('src/styles/variables.scss');
    });
  });

  describe('Relative Path Resolution', () => {
    test('should resolve relative paths with normalized separators', async () => {
      // 创建测试文件结构
      const stylesDir = path.join(testProjectPath, 'src', 'styles');
      const componentsDir = path.join(stylesDir, 'components');
      await fs.ensureDir(componentsDir);

      const variablesFile = path.join(stylesDir, 'variables.scss');
      const buttonFile = path.join(componentsDir, 'button.scss');

      await fs.writeFile(variablesFile, '$primary-color: blue;');
      await fs.writeFile(buttonFile, '.button { color: $primary-color; }');

      // 测试从 button.scss 导入 variables.scss
      const resolvedPath = resolver.resolveRelativePath('../variables', buttonFile);
      
      // 应该返回使用正斜杠的路径
      expect(resolvedPath).toBe('src/styles/variables');
      expect(resolvedPath).not.toContain('\\');
    });

    test('should handle deeply nested relative paths', async () => {
      // 创建深层嵌套的目录结构
      const deepDir = path.join(testProjectPath, 'src', 'styles', 'components', 'forms', 'inputs');
      await fs.ensureDir(deepDir);

      const variablesFile = path.join(testProjectPath, 'src', 'styles', 'variables.scss');
      const inputFile = path.join(deepDir, 'input.scss');

      await fs.writeFile(variablesFile, '$primary-color: blue;');
      await fs.writeFile(inputFile, '.input { color: $primary-color; }');

      // 测试从深层目录导入根级变量文件
      const resolvedPath = resolver.resolveRelativePath('../../../../variables', inputFile);

      // 应该返回使用正斜杠的路径
      expect(resolvedPath).toBe('src/variables');
      expect(resolvedPath).not.toContain('\\');
    });
  });

  describe('Tilde Alias Resolution', () => {
    test('should resolve tilde alias with normalized separators', () => {
      const tildeImport = '~element-ui/packages/theme-chalk/src/index';
      const resolved = resolver.resolveTildeAlias(tildeImport);
      
      // 应该返回使用正斜杠的路径
      expect(resolved).not.toContain('\\');
      expect(resolved).toContain('/');
      expect(resolved).toContain('node_modules/element-ui/packages/theme-chalk/src/index');
    });
  });

  describe('Import to Use Conversion', () => {
    test('should convert import statements with normalized paths', async () => {
      // 创建测试文件结构
      const stylesDir = path.join(testProjectPath, 'src', 'styles');
      const componentsDir = path.join(stylesDir, 'components');
      await fs.ensureDir(componentsDir);

      const variablesFile = path.join(stylesDir, 'variables.scss');
      const buttonFile = path.join(componentsDir, 'button.scss');

      await fs.writeFile(variablesFile, '$primary-color: blue;');
      await fs.writeFile(buttonFile, '.button { color: $primary-color; }');

      const importStatement = '@import "../variables";';
      const useStatement = resolver.convertImportToUse(importStatement, buttonFile);
      
      // 检查生成的 @use 语句使用正斜杠
      expect(useStatement).toContain('@use \'src/styles/variables.scss\' as *;');
      expect(useStatement).not.toContain('\\');
    });
  });

  describe('Cross-platform Compatibility', () => {
    test('should handle paths consistently across platforms', () => {
      const testCases = [
        { input: 'src/styles/variables.scss', expected: 'src/styles/variables.scss' },
        { input: 'src\\styles\\variables.scss', expected: 'src/styles/variables.scss' },
        { input: 'src/styles\\components\\button.scss', expected: 'src/styles/components/button.scss' },
        { input: 'src\\styles/components\\button.scss', expected: 'src/styles/components/button.scss' }
      ];

      testCases.forEach(({ input, expected }) => {
        const normalized = resolver.normalizePath(input);
        expect(normalized).toBe(expected);
        expect(normalized).not.toContain('\\');
      });
    });

    test('should generate consistent namespace names regardless of path separators', () => {
      const windowsPath = 'variables.scss';
      const unixPath = 'variables.scss';

      const windowsNamespace = resolver.generateNamespace(windowsPath);
      const unixNamespace = resolver.generateNamespace(unixPath);

      expect(windowsNamespace).toBe(unixNamespace);
      expect(windowsNamespace).toBe('variables');
    });
  });
});
