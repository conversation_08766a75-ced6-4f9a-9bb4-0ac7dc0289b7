const fs = require('fs-extra');
const path = require('path');
const { expect } = require('@jest/globals');
const SassVariableRegistry = require('../../../src/features/sass/SassVariableRegistry');

describe('SassVariableRegistry - Windows Path Compatibility', () => {
  let testProjectPath;
  let registry;

  beforeEach(async () => {
    testProjectPath = path.join(__dirname, '../fixtures/variable-registry-test');
    await fs.ensureDir(testProjectPath);

    registry = new SassVariableRegistry(testProjectPath);
  });

  afterEach(async () => {
    if (await fs.pathExists(testProjectPath)) {
      await fs.remove(testProjectPath);
    }
  });

  describe('Path Normalization', () => {
    test('should normalize Windows backslashes to forward slashes', () => {
      const windowsPath = 'src\\styles\\variables.scss';
      const normalized = registry.normalizePath(windowsPath);
      
      expect(normalized).toBe('src/styles/variables.scss');
    });

    test('should handle mixed path separators', () => {
      const mixedPath = 'src/styles\\components\\button.scss';
      const normalized = registry.normalizePath(mixedPath);
      
      expect(normalized).toBe('src/styles/components/button.scss');
    });

    test('should handle empty or null paths', () => {
      expect(registry.normalizePath('')).toBe('');
      expect(registry.normalizePath(null)).toBe(null);
      expect(registry.normalizePath(undefined)).toBe(undefined);
    });

    test('should preserve forward slashes', () => {
      const unixPath = 'src/styles/variables.scss';
      const normalized = registry.normalizePath(unixPath);
      
      expect(normalized).toBe('src/styles/variables.scss');
    });
  });

  describe('Import Suggestions with Normalized Paths', () => {
    test('should generate import suggestions with forward slashes', async () => {
      // 创建测试文件结构
      const stylesDir = path.join(testProjectPath, 'src', 'styles');
      const componentsDir = path.join(stylesDir, 'components');
      await fs.ensureDir(componentsDir);

      // 创建变量文件
      const variablesFile = path.join(stylesDir, 'variables.scss');
      await fs.writeFile(variablesFile, `
$primary-color: #007bff;
$secondary-color: #6c757d;
$margin-base: 16px;
`);

      // 创建组件文件
      const buttonFile = path.join(componentsDir, 'button.scss');
      await fs.writeFile(buttonFile, `
.button {
  color: $primary-color;
  margin: $margin-base;
}
`);

      // 构建注册表
      await registry.buildRegistry();

      // 生成导入建议
      const suggestions = registry.generateImportSuggestions(buttonFile, ['primary-color', 'margin-base']);

      expect(suggestions).toHaveLength(1);
      expect(suggestions[0].importPath).toBe('../variables');
      expect(suggestions[0].importPath).not.toContain('\\');
      expect(suggestions[0].statement).toBe('@use \'../variables\' as *;');
    });

    test('should handle deeply nested component files', async () => {
      // 创建深层嵌套的目录结构
      const deepDir = path.join(testProjectPath, 'src', 'styles', 'components', 'forms', 'inputs');
      await fs.ensureDir(deepDir);

      // 创建根级变量文件
      const variablesFile = path.join(testProjectPath, 'src', 'styles', 'variables.scss');
      await fs.writeFile(variablesFile, `
$input-border-color: #ccc;
$input-focus-color: #007bff;
`);

      // 创建深层嵌套的组件文件
      const inputFile = path.join(deepDir, 'input.scss');
      await fs.writeFile(inputFile, `
.input {
  border-color: $input-border-color;
  &:focus {
    border-color: $input-focus-color;
  }
}
`);

      // 构建注册表
      await registry.buildRegistry();

      // 生成导入建议
      const suggestions = registry.generateImportSuggestions(inputFile, ['input-border-color', 'input-focus-color']);

      expect(suggestions).toHaveLength(1);
      expect(suggestions[0].importPath).toBe('../../../variables');
      expect(suggestions[0].importPath).not.toContain('\\');
      expect(suggestions[0].statement).toBe('@use \'../../../variables\' as *;');
    });

    test('should handle relative paths from different directory levels', async () => {
      // 创建多层目录结构
      const level1Dir = path.join(testProjectPath, 'src', 'styles');
      const level2Dir = path.join(level1Dir, 'components');
      const level3Dir = path.join(level2Dir, 'forms');
      
      await fs.ensureDir(level3Dir);

      // 在不同层级创建变量文件
      const rootVarsFile = path.join(level1Dir, 'variables.scss');
      const componentVarsFile = path.join(level2Dir, 'component-variables.scss');
      
      await fs.writeFile(rootVarsFile, '$root-color: red;');
      await fs.writeFile(componentVarsFile, '$component-color: blue;');

      // 创建使用变量的文件
      const formFile = path.join(level3Dir, 'form.scss');
      await fs.writeFile(formFile, `
.form {
  color: $root-color;
  background: $component-color;
}
`);

      // 构建注册表
      await registry.buildRegistry();

      // 生成导入建议
      const suggestions = registry.generateImportSuggestions(formFile, ['root-color', 'component-color']);

      // 检查所有建议都使用正斜杠
      suggestions.forEach(suggestion => {
        expect(suggestion.importPath).not.toContain('\\');
        expect(suggestion.statement).not.toContain('\\');
      });

      // 应该有两个建议，一个指向根变量文件，一个指向组件变量文件
      expect(suggestions).toHaveLength(2);
      
      const rootSuggestion = suggestions.find(s => s.importPath.includes('../../variables'));
      const componentSuggestion = suggestions.find(s => s.importPath.includes('../component-variables'));
      
      expect(rootSuggestion).toBeDefined();
      expect(componentSuggestion).toBeDefined();
    });
  });

  describe('Cross-platform File Path Handling', () => {
    test('should handle file paths consistently across platforms', async () => {
      // 模拟不同平台的路径格式
      const testFiles = [
        { platform: 'unix', path: 'src/styles/variables.scss' },
        { platform: 'windows', path: 'src\\styles\\variables.scss' }
      ];

      for (const testFile of testFiles) {
        const stylesDir = path.join(testProjectPath, 'src', 'styles');
        await fs.ensureDir(stylesDir);

        const variablesFile = path.join(stylesDir, 'variables.scss');
        await fs.writeFile(variablesFile, '$test-color: red;');

        // 重新创建注册表以确保清洁状态
        registry = new SassVariableRegistry(testProjectPath);
        await registry.buildRegistry();

        // 验证变量被正确注册
        expect(registry.hasVariable('test-color')).toBe(true);

        // 清理
        await fs.remove(testProjectPath);
        await fs.ensureDir(testProjectPath);
      }
    });
  });
});
