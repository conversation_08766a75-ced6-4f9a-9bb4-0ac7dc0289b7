const fs = require('fs-extra');
const path = require('path');
const { performance } = require('perf_hooks');
const SassVariableResolver = require('../../src/features/sass/SassVariableResolver');

describe('SassVariableResolver Performance Tests', () => {
  let testProjectPath;
  let resolver;

  beforeAll(async () => {
    testProjectPath = path.join(__dirname, '../../fixtures/performance-test');
    await fs.ensureDir(testProjectPath);

    resolver = new SassVariableResolver(testProjectPath);
  });

  afterAll(async () => {
    if (await fs.pathExists(testProjectPath)) {
      await fs.remove(testProjectPath);
    }
  });

  test('should handle large number of variable files efficiently', async () => {
    // 创建大量变量文件和使用文件
    const numVariableFiles = 10;
    const numComponentFiles = 50;
    const variablesPerFile = 20;

    console.log(`创建测试文件: ${numVariableFiles} 个变量文件, ${numComponentFiles} 个组件文件`);

    // 创建变量文件
    for (let i = 0; i < numVariableFiles; i++) {
      let content = '';
      for (let j = 0; j < variablesPerFile; j++) {
        content += `$var-${i}-${j}: ${j}px;\n`;
      }
      await fs.writeFile(
        path.join(testProjectPath, `variables-${i}.scss`),
        content
      );
    }

    // 创建组件文件，每个使用一些变量
    for (let i = 0; i < numComponentFiles; i++) {
      let content = `.component-${i} {\n`;

      // 每个组件使用 3-5 个随机变量
      const numVars = 3 + Math.floor(Math.random() * 3);
      for (let j = 0; j < numVars; j++) {
        const varFileIdx = Math.floor(Math.random() * numVariableFiles);
        const varIdx = Math.floor(Math.random() * variablesPerFile);
        content += `  property-${j}: $var-${varFileIdx}-${varIdx};\n`;
      }

      content += '}\n';

      await fs.writeFile(
        path.join(testProjectPath, `component-${i}.scss`),
        content
      );
    }

    // 测试初始化性能
    const initStart = performance.now();
    const initialized = await resolver.initialize();
    const initEnd = performance.now();

    expect(initialized).toBe(true);
    console.log(`初始化时间: ${(initEnd - initStart).toFixed(2)}ms`);

    // 初始化时间应该在合理范围内（<2秒）
    expect(initEnd - initStart).toBeLessThan(2000);

    // 测试单个文件分析性能
    const analysisStart = performance.now();
    const analysis = await resolver.analyzeFileDependencies(
      path.join(testProjectPath, 'component-0.scss')
    );
    const analysisEnd = performance.now();

    expect(analysis).toBeDefined();
    console.log(`单文件分析时间: ${(analysisEnd - analysisStart).toFixed(2)}ms`);

    // 单文件分析应该很快（<50ms）
    expect(analysisEnd - analysisStart).toBeLessThan(50);

    // 测试项目级别问题检查性能
    const sassFiles = [];
    for (let i = 0; i < numComponentFiles; i++) {
      sassFiles.push(path.join(testProjectPath, `component-${i}.scss`));
    }

    const checkStart = performance.now();
    const { issues, summary } = await resolver.checkVariableIssues(sassFiles);
    const checkEnd = performance.now();

    console.log(`项目检查时间: ${(checkEnd - checkStart).toFixed(2)}ms`);
    console.log(`发现问题: ${issues.length}, 总文件: ${summary.totalFiles}`);

    // 项目级别检查应该在合理时间内完成（<5秒）
    expect(checkEnd - checkStart).toBeLessThan(5000);
  });

  test('should handle files with many variables efficiently', async () => {
    // 创建包含大量变量的文件
    const largeVariableCount = 1000;
    let variableContent = '';

    for (let i = 0; i < largeVariableCount; i++) {
      variableContent += `$large-var-${i}: ${i}px;\n`;
    }

    await fs.writeFile(
      path.join(testProjectPath, 'large-variables.scss'),
      variableContent
    );

    // 创建使用很多变量的文件
    let componentContent = '.large-component {\n';
    for (let i = 0; i < 100; i++) {
      const varIdx = Math.floor(Math.random() * largeVariableCount);
      componentContent += `  prop-${i}: $large-var-${varIdx};\n`;
    }
    componentContent += '}\n';

    await fs.writeFile(
      path.join(testProjectPath, 'large-component.scss'),
      componentContent
    );

    const resolver2 = new SassVariableResolver(testProjectPath);

    const start = performance.now();
    await resolver2.initialize();
    const analysis = await resolver2.analyzeFileDependencies(
      path.join(testProjectPath, 'large-component.scss')
    );
    const end = performance.now();

    expect(analysis).toBeDefined();
    expect(analysis.usedVariables.length).toBeGreaterThan(90); // 应该检测到大部分变量

    console.log(`大文件处理时间: ${(end - start).toFixed(2)}ms`);
    console.log(`检测到变量数量: ${analysis.usedVariables.length}`);

    // 处理大文件应该在合理时间内（<1秒）
    expect(end - start).toBeLessThan(1000);
  });
});
