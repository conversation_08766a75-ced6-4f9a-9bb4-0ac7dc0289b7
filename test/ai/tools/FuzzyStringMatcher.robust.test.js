const FuzzyStringMatcher = require('../../../src/ai/tools/FuzzyStringMatcher');

describe('FuzzyStringMatcher - Algorithm Robustness Tests', () => {
  let matcher;

  beforeEach(() => {
    matcher = new FuzzyStringMatcher({ verbose: false });
  });

  describe('position mapping validation', () => {
    it('should validate position mapping correctness', () => {
      const testCases = [
        {
          original: `function test() {
  if (true) {
    console.log('hello');
  }
}`,
          normalized: `function test() {
if (true) {
console.log('hello');
}
}`,
          target: 'console.log'
        },
        {
          original: `const config = {
  host: 'localhost',
    port: 3000,
      debug: true
};`,
          normalized: `const config = {
host: 'localhost',
port: 3000,
debug: true
};`,
          target: 'port: 3000'
        },
        {
          original: `\tfunction\ttabbed() {\n\t\treturn\t'value';\n\t}`,
          normalized: `function tabbed() {\nreturn 'value';\n}`,
          target: 'return'
        }
      ];

      testCases.forEach(({ original, normalized, target }, index) => {
        const normalizedIndex = normalized.indexOf(target);
        const expectedIndex = original.indexOf(target);
        
        expect(normalizedIndex).toBeGreaterThan(-1);
        expect(expectedIndex).toBeGreaterThan(-1);

        const result = matcher.findOriginalTextFromNormalized(
          original, 
          normalized, 
          normalizedIndex, 
          target.length
        );

        expect(result.index).toBe(expectedIndex);
        expect(result.text).toBe(target);
      });
    });

    it('should handle edge cases in position mapping', () => {
      const original = `\n\n\n  function test() {\n    return 'value';\n  }\n\n`;
      const normalized = `function test() {\nreturn 'value';\n}`;
      
      const target = 'return';
      const normalizedIndex = normalized.indexOf(target);
      const expectedIndex = original.indexOf(target);

      const result = matcher.findOriginalTextFromNormalized(
        original,
        normalized,
        normalizedIndex,
        target.length
      );

      expect(result.index).toBe(expectedIndex);
      expect(result.text).toBe(target);
    });
  });

  describe('stress testing', () => {
    it('should handle files with thousands of lines', () => {
      let largeFile = '';
      for (let i = 0; i < 5000; i++) {
        largeFile += `function func${i}() {\n  return ${i};\n}\n\n`;
      }

      const target = 'function func2500() {\n  return 2500;\n}';
      const pattern = target.replace(/\n/g, '\n');

      const start = Date.now();
      const result = matcher.findMatch(largeFile, pattern);
      const duration = Date.now() - start;

      expect(result.found).toBe(true);
      expect(duration).toBeLessThan(2000); // 应该在2秒内完成
    });

    it('should handle complex nesting levels', () => {
      let deepCode = '';
      for (let i = 0; i < 50; i++) {
        deepCode += `${'  '.repeat(i)}if (condition${i}) {\n`;
      }
      deepCode += `${'  '.repeat(50)}console.log('deep level');\n`;
      for (let i = 49; i >= 0; i--) {
        deepCode += `${'  '.repeat(i)}}\n`;
      }

      const pattern = `console.log('deep level');`;
      const result = matcher.findMatch(deepCode, pattern);

      expect(result.found).toBe(true);
    });
  });

  describe('replacement accuracy validation', () => {
    it('should validate replacement position accuracy', () => {
      const testCases = [
        {
          name: 'simple function replacement',
          target: `function oldName() {
  return 'value';
}`,
          pattern: `function oldName() {
  return 'value';
}`,
          replacement: `function newName() {
  return 'value';
}`,
          expected: 'newName'
        },
        {
          name: 'indented code replacement',
          target: `class MyClass {
  method() {
    if (true) {
      return 'old';
    }
  }
}`,
          pattern: `if (true) {
      return 'old';
    }`,
          replacement: `if (true) {
      return 'new';
    }`,
          expected: 'new'
        },
        {
          name: 'mixed indentation replacement',
          target: `function test() {
\tif (condition) {
\t  return 'tab-space';
\t}
}`,
          pattern: `if (condition) {
\t  return 'tab-space';
\t}`,
          replacement: `if (condition) {
\t  return 'replaced';
\t}`,
          expected: 'replaced'
        }
      ];

      testCases.forEach(({ name, target, pattern, replacement, expected }) => {
        const result = matcher.smartReplace(target, pattern, replacement);
        
        expect(result.success).toBe(true);
        expect(result.result).toContain(expected);
      });
    });

    it('should handle boundary replacements correctly', () => {
      const target = `const start = 'beginning';
const middle = 'center';
const end = 'finish';`;

      // 测试开头替换
      const startResult = matcher.smartReplace(target, `const start = 'beginning';`, `const start = 'modified';`);
      expect(startResult.success).toBe(true);
      expect(startResult.result).toContain('modified');
      expect(startResult.result).toContain('center');

      // 测试结尾替换
      const endResult = matcher.smartReplace(target, `const end = 'finish';`, `const end = 'done';`);
      expect(endResult.success).toBe(true);
      expect(endResult.result).toContain('done');
      expect(endResult.result).toContain('center');
    });
  });

  describe('error recovery and suggestions', () => {
    it('should provide meaningful suggestions for common mistakes', () => {
      const testCases = [
        {
          target: 'function test() { return true; }',
          pattern: '  function test() { return true; }  ', // 额外空格
          expectedSuggestion: 'whitespace'
        },
        {
          target: 'line1\nline2',
          pattern: 'line1\r\nline2', // 不同换行符
          expectedSuggestion: 'line_endings'
        },
        {
          target: 'function test() {\n  return true;\n}',
          pattern: 'function test() {\n    return true;\n}', // 缩进不同
          expectedSuggestion: 'indentation'
        }
      ];

      testCases.forEach(({ target, pattern, expectedSuggestion }) => {
        const result = matcher.findMatch(target, pattern);
        
        if (!result.found) {
          expect(result.suggestions).toBeDefined();
          expect(result.suggestions.some(s => s.type === expectedSuggestion)).toBe(true);
        }
      });
    });

    it('should handle graceful degradation on algorithm failures', () => {
      const problematicTarget = 'function test() { return "unclosed string; }';
      const problematicPattern = 'function test() { return "different string; }';

      const result = matcher.findMatch(problematicTarget, problematicPattern);
      
      expect(result).toBeDefined();
      expect(result.found).toBe(false);
      expect(result.error || result.suggestions).toBeDefined();
    });
  });

  describe('memory and performance validation', () => {
    it('should not cause memory leaks with repeated operations', () => {
      const target = 'function test() { return true; }';
      const pattern = 'function test() { return true; }';

      // 执行大量重复操作
      for (let i = 0; i < 1000; i++) {
        const result = matcher.findMatch(target, pattern);
        expect(result.found).toBe(true);
      }
    });

    it('should handle extreme content sizes gracefully', () => {
      const megabyteString = 'x'.repeat(1024 * 1024); // 1MB
      const pattern = 'x'.repeat(100);

      const start = Date.now();
      const result = matcher.findMatch(megabyteString, pattern);
      const duration = Date.now() - start;

      expect(result).toBeDefined();
      expect(duration).toBeLessThan(10000); // 10秒内完成
    });
  });

  describe('integration with modified algorithm', () => {
    it('should work correctly with position mapping', () => {
      const original = `function test() {
  const x = 1;
    const y = 2;
      return x + y;
}`;
      const normalized = `function test() {
const x = 1;
const y = 2;
return x + y;
}`;

      // 测试精确位置映射功能
      const testPositions = ['const x', 'const y', 'return x'];
      testPositions.forEach(testString => {
        const normalizedIndex = normalized.indexOf(testString);
        const originalIndex = original.indexOf(testString);
        
        if (normalizedIndex !== -1 && originalIndex !== -1) {
          const result = matcher.buildPreciseMapping(
            original, 
            normalized, 
            normalizedIndex, 
            testString.length
          );
          
          expect(result).toBeDefined();
          expect(result.index).toBeGreaterThanOrEqual(0);
          expect(result.index).toBeLessThan(original.length);
          expect(result.text).toContain(testString.trim());
        }
      });
    });

    it('should handle text normalization correctly', () => {
      // 直接测试归一化的核心功能
      const input1 = '  function test() {\n    return true;\n  }  ';
      const result1 = matcher.normalizeText(input1);
      expect(result1).toBe('function test() {\nreturn true;\n}');

      const input2 = '\t\tconst x = 1;\n\t\treturn x;';
      const result2 = matcher.normalizeText(input2);
      expect(result2).toBe('const x = 1;\nreturn x;');

      // 测试基本归一化功能正常工作
      expect(typeof result1).toBe('string');
      expect(result1.length).toBeGreaterThan(0);
    });

    it('should handle position mapping edge cases', () => {
      // 测试极端情况下的位置映射
      const original = '\n\n  \t  function test() {\n    return;\n  }\n\n';
      const normalized = 'function test() {\nreturn;\n}';
      
      const result = matcher.findOriginalTextFromNormalized(
        original, 
        normalized, 
        normalized.indexOf('function'), 
        'function'.length
      );
      
      expect(result.index).toBe(original.indexOf('function'));
      expect(result.text).toBe('function');
    });
  });
});