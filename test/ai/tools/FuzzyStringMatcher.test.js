const FuzzyStringMatcher = require('../../../src/ai/tools/FuzzyStringMatcher');

describe('FuzzyStringMatcher', () => {
  let matcher;

  beforeEach(() => {
    matcher = new FuzzyStringMatcher({ verbose: false });
  });

  describe('findMatch', () => {
    it('should find exact matches', () => {
      const target = 'function test() {\n  return "hello";\n}';
      const pattern = 'function test() {\n  return "hello";\n}';

      const result = matcher.findMatch(target, pattern);

      expect(result.found).toBe(true);
      expect(result.method).toBe('exact');
      expect(result.index).toBe(0);
    });

    it('should find matches with different indentation', () => {
      const target = `    renderContent(h, { node, data }) { // 添加并隐藏id  以便获取DOM时能获取到对应id
      return (
        <div class='connect_id_title'>
          {data.title} <span class='connect_id_number' style='opacity:0'>{data.id}</span>
        </div>
      )
    },`;

      const pattern = `renderContent(h, { node, data }) { // 添加并隐藏id  以便获取DOM时能获取到对应id
    return (
      <div class='connect_id_title'>
        {data.title} <span class='connect_id_number' style='opacity:0'>{data.id}</span>
      </div>
    )
  },`;

      const result = matcher.findMatch(target, pattern);

      expect(result.found).toBe(true);
      expect(['normalized', 'fuzzy']).toContain(result.method);
    });

    it('should find matches with different line endings', () => {
      const target = 'line1\nline2\nline3';
      const pattern = 'line1\r\nline2\r\nline3';

      const result = matcher.findMatch(target, pattern);

      expect(result.found).toBe(true);
      expect(result.method).toBe('normalized');
    });

    it('should find matches ignoring leading/trailing whitespace', () => {
      const target = '  function test() {\n    return true;\n  }  ';
      const pattern = 'function test() {\n  return true;\n}';

      const result = matcher.findMatch(target, pattern);

      expect(result.found).toBe(true);
    });

    it('should return suggestions when no match found', () => {
      const target = 'function test() { return "hello"; }';
      const pattern = '  function test() { return "hello"; }  '; // 额外的空格

      const result = matcher.findMatch(target, pattern);

      if (!result.found) {
        expect(result.suggestions).toBeDefined();
        expect(Array.isArray(result.suggestions)).toBe(true);
      }
    });
  });

  describe('smartReplace', () => {
    it('should perform exact replacement', () => {
      const target = 'Hello world! This is a test.';
      const pattern = 'world';
      const replacement = 'universe';

      const result = matcher.smartReplace(target, pattern, replacement);

      expect(result.success).toBe(true);
      expect(result.result).toBe('Hello universe! This is a test.');
      expect(result.method).toBe('exact');
    });

    it('should handle indentation differences in replacement', () => {
      const target = `    renderContent(h, { node, data }) {
      return (
        <div class='connect_id_title'>
          {data.title}
        </div>
      )
    },`;

      const pattern = `renderContent(h, { node, data }) {
    return (
      <div class='connect_id_title'>
        {data.title}
      </div>
    )
  },`;

      const replacement = `renderContent(h, { node, data }) {
    return h('div', { class: 'connect_id_title' }, [
      h('span', null, data.title)
    ])
  },`;

      const result = matcher.smartReplace(target, pattern, replacement);

      expect(result.success).toBe(true);
      expect(result.result).toContain('return h(');
    });

    it('should return error when no match found', () => {
      const target = 'function test() { return true; }';
      const pattern = 'function notFound() { return false; }';
      const replacement = 'function replaced() { return null; }';

      const result = matcher.smartReplace(target, pattern, replacement);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });
  });

  describe('normalizeText', () => {
    it('should remove leading whitespace', () => {
      const input = '  hello\n  world';
      const expected = 'hello\nworld';

      expect(matcher.normalizeText(input)).toBe(expected);
    });

    it('should remove trailing whitespace', () => {
      const input = 'hello  \nworld  ';
      const expected = 'hello\nworld';

      expect(matcher.normalizeText(input)).toBe(expected);
    });

    it('should normalize line endings', () => {
      const input = 'line1\r\nline2\rline3\n';
      const expected = 'line1\nline2\nline3';

      expect(matcher.normalizeText(input)).toBe(expected);
    });
  });

  describe('generateSuggestions', () => {
    it('should suggest indentation fix', () => {
      const target = 'function test() { return true; }';
      const pattern = '  function test() { return true; }';

      const suggestions = matcher.generateSuggestions(target, pattern);

      expect(suggestions.length).toBeGreaterThan(0);
      expect(suggestions.some(s => s.type === 'indentation')).toBe(true);
    });

    it('should suggest line ending fix', () => {
      const target = 'line1\nline2';
      const pattern = 'line1\r\nline2';

      const suggestions = matcher.generateSuggestions(target, pattern);

      expect(suggestions.some(s => s.type === 'line_endings')).toBe(true);
    });

    it('should suggest whitespace fix', () => {
      const target = 'hello world';
      const pattern = 'hello    world';

      const suggestions = matcher.generateSuggestions(target, pattern);

      expect(suggestions.some(s => s.type === 'whitespace')).toBe(true);
    });
  });

  describe('edge cases', () => {
    it('should handle empty strings', () => {
      const result = matcher.findMatch('', '');
      expect(result.found).toBe(false);
      expect(result.error).toContain('为空');
    });

    it('should handle null/undefined inputs', () => {
      const result1 = matcher.findMatch(null, 'pattern');
      const result2 = matcher.findMatch('target', null);

      expect(result1.found).toBe(false);
      expect(result2.found).toBe(false);
    });

    it('should handle very long strings', () => {
      const longTarget = 'x'.repeat(10000) + '\nfunction test() { return true; }\n' + 'y'.repeat(10000);
      const pattern = 'function test() { return true; }';

      const result = matcher.findMatch(longTarget, pattern);

      expect(result.found).toBe(true);
    });
  });

  describe('real world examples', () => {
    it('should handle Vue renderContent function with JSX', () => {
      const target = `    renderContent(h, { node, data }) { // 添加并隐藏id  以便获取DOM时能获取到对应id
      return (
        <div class='connect_id_title'>
          {data.title} <span class='connect_id_number' style='opacity:0'>{data.id}</span>
        </div>
      )
    },`;

      const pattern = `renderContent(h, { node, data }) { // 添加并隐藏id  以便获取DOM时能获取到对应id
    return (
      <div class='connect_id_title'>
        {data.title} <span class='connect_id_number' style='opacity:0'>{data.id}</span>
      </div>
    )
  },`;

      const replacement = `renderContent(h, { node, data }) {
    return h('div', { class: 'connect_id_title' }, [
      h('span', null, data.title),
      h('span', { class: 'connect_id_number', style: { opacity: 0 } }, data.id)
    ])
  },`;

      const result = matcher.smartReplace(target, pattern, replacement);

      expect(result.success).toBe(true);
      expect(result.result).toContain('return h(');
      expect(result.result).toContain('opacity: 0');
    });
  });
});
