const FuzzyStringMatcher = require('../../../src/ai/tools/FuzzyStringMatcher');

describe('FuzzyStringMatcher - Large File Precision Tests', () => {
  let matcher;

  beforeEach(() => {
    matcher = new FuzzyStringMatcher({ verbose: false });
  });

  describe('Vue component real-world scenarios', () => {
    it('should handle Vue renderContent method replacement correctly', () => {
      // 模拟真实的大型 Vue 组件文件结构
      const largeVueComponent = `<template>
  <div>
    <div class="app-container calendar-list-container">
      <!-- 大量模板内容 -->
      ${'<div>Content line</div>\n'.repeat(100)}
    </div>
  </div>
</template>

<script>
import global_ from '@/common/Global'
import { fetchTree } from '@/api/admin/menu/index'
import { doRequestPost } from '@/utils/request'

export default {
  data() {
    return {
      loading: false,
      form: {
        code: '',
        href: '',
        icon: '',
        id: '',
        level: '',
        parentId: '',
        sortNo: '',
        status: '',
        title: '',
        type: '',
      },
      ${'someProperty: "value",\n      '.repeat(50)}
      treeData: [],
      defaultProps: {
        children: 'children',
        label: 'title',
      }
    }
  },
  methods: {
    getCheckbox(data) {
      // 大量方法内容
      ${'if (condition) { console.log("test"); }\n      '.repeat(20)}
    },
    renderContent(h, { node, data }) {
      // 添加并隐藏id  以便获取DOM时能获取到对应id
      return (
        <div class="connect_id_title">
          {data.title}{' '}
          <span class="connect_id_number" style="opacity:0">
            {data.id}
          </span>
        </div>
      )
    },
    init(uposId) {
      this.uposId = uposId
      if (this.uposId) {
        this.getList()
      }
      ${'this.someMethod();\n      '.repeat(30)}
    }
  }
}
</script>`;

      const oldPattern = `renderContent(h, { node, data }) {
      // 添加并隐藏id  以便获取DOM时能获取到对应id
      return (
        <div class="connect_id_title">
          {data.title}{' '}
          <span class="connect_id_number" style="opacity:0">
            {data.id}
          </span>
        </div>
      )
    }`;

      const newReplacement = `renderContent(h, { node, data }) {
      // 添加并隐藏id  以便获取DOM时能获取到对应id
      return h('div', { class: 'connect_id_title' }, [
        h('span', null, data.title),
        h('span', { class: 'connect_id_number', style: { opacity: 0 } }, data.id)
      ])
    }`;

      const result = matcher.smartReplace(largeVueComponent, oldPattern, newReplacement);

      expect(result.success).toBe(true);
      expect(result.result).toContain('return h(');
      expect(result.result).toContain('opacity: 0');
      
      // 验证文件结构没有被破坏
      expect(result.result).toContain('<template>');
      expect(result.result).toContain('<script>');
      expect(result.result).toContain('export default');
      expect(result.result).toContain('getCheckbox(data)');
      expect(result.result).toContain('init(uposId)');
      
      // 验证只替换了目标方法
      const renderContentCount = (result.result.match(/renderContent/g) || []).length;
      expect(renderContentCount).toBe(1);
      
      // 验证原有JSX代码不再存在
      expect(result.result).not.toContain('return (');
      expect(result.result).not.toContain('<div class="connect_id_title">');
    });

    it('should handle indentation variations in large files', () => {
      const fileWithMixedIndentation = `function outer() {
  ${'// line comment\n  '.repeat(100)}
    renderContent(h, { node, data }) {
      return (
        <div class="connect_id_title">
          {data.title}
        </div>
      )
    },
  ${'// another comment\n  '.repeat(100)}
}`;

      const pattern = `renderContent(h, { node, data }) {
      return (
        <div class="connect_id_title">
          {data.title}
        </div>
      )
    }`;

      const replacement = `renderContent(h, { node, data }) {
      return h('div', { class: 'connect_id_title' }, data.title)
    }`;

      const result = matcher.smartReplace(fileWithMixedIndentation, pattern, replacement);

      expect(result.success).toBe(true);
      expect(result.result).toContain('return h(');
      expect(result.result).not.toContain('return (');
    });

    it('should preserve file integrity during replacement', () => {
      const complexFile = `import React from 'react'
import { Component } from 'react'

${'// Complex import statements\n'.repeat(50)}

class MyComponent extends Component {
  constructor(props) {
    super(props)
    this.state = {
      ${'data: {},\n      '.repeat(20)}
    }
  }

  componentDidMount() {
    ${'console.log("mount");\n    '.repeat(10)}
  }

  renderContent(h, { node, data }) {
    return (
      <div class="target">
        {data.title}
      </div>
    )
  }

  render() {
    return (
      <div>
        ${'<span>Content</span>\n        '.repeat(100)}
      </div>
    )
  }
}

export default MyComponent`;

      const pattern = `renderContent(h, { node, data }) {
    return (
      <div class="target">
        {data.title}
      </div>
    )
  }`;

      const replacement = `renderContent(h, { node, data }) {
    return h('div', { class: 'target' }, data.title)
  }`;

      const result = matcher.smartReplace(complexFile, pattern, replacement);

      expect(result.success).toBe(true);
      
      // 验证文件结构完整性
      expect(result.result).toContain('import React from');
      expect(result.result).toContain('class MyComponent extends Component');
      expect(result.result).toContain('componentDidMount()');
      expect(result.result).toContain('render()');
      expect(result.result).toContain('export default MyComponent');
      
      // 验证只有目标方法被替换
      expect(result.result).toContain('return h(');
      const renderContentMatches = result.result.match(/renderContent/g);
      expect(renderContentMatches).toHaveLength(1);
    });
  });

  describe('precision validation', () => {
    it('should not affect similar but different patterns', () => {
      const fileWithSimilarMethods = `class Component {
  renderContent(h, { node, data }) {
    return <div>{data.title}</div>
  }

  renderOtherContent(h, { node, data }) {
    return <div>{data.title}</div>
  }

  renderContentWrapper() {
    return this.renderContent()
  }
}`;

      const pattern = `renderContent(h, { node, data }) {
    return <div>{data.title}</div>
  }`;

      const replacement = `renderContent(h, { node, data }) {
    return h('div', null, data.title)
  }`;

      const result = matcher.smartReplace(fileWithSimilarMethods, pattern, replacement);

      expect(result.success).toBe(true);
      expect(result.result).toContain('return h(');
      
      // 验证其他方法未被影响
      expect(result.result).toContain('renderOtherContent(h, { node, data }) {');
      expect(result.result).toContain('renderContentWrapper()');
      expect(result.result).toContain('return this.renderContent()');
    });

    it('should handle edge cases in large files', () => {
      const edgeCaseFile = `// File with edge cases
/*
Multi-line comment with renderContent reference
*/

const config = {
  template: \`
    renderContent(h, { node, data }) {
      return "string template"
    }
  \`
}

function actualMethod() {
  renderContent(h, { node, data }) {
    return <div>actual</div>
  }
}`;

      const pattern = `renderContent(h, { node, data }) {
    return <div>actual</div>
  }`;

      const replacement = `renderContent(h, { node, data }) {
    return h('div', null, 'actual')
  }`;

      const result = matcher.smartReplace(edgeCaseFile, pattern, replacement);

      expect(result.success).toBe(true);
      expect(result.result).toContain('return h(');
      
      // 验证模板字符串未被影响
      expect(result.result).toContain('return "string template"');
      expect(result.result).toContain('template: `');
    });
  });

  describe('performance validation', () => {
    it('should handle very large files efficiently', () => {
      const megabyteFile = `// Large file test
${'// Comment line\\n'.repeat(10000)}

function target() {
  renderContent(h, { node, data }) {
    return <div>target</div>
  }
}

${'// More content\\n'.repeat(10000)}`;

      const pattern = `renderContent(h, { node, data }) {
    return <div>target</div>
  }`;

      const replacement = `renderContent(h, { node, data }) {
    return h('div', null, 'target')
  }`;

      const start = Date.now();
      const result = matcher.smartReplace(megabyteFile, pattern, replacement);
      const duration = Date.now() - start;

      expect(result.success).toBe(true);
      expect(duration).toBeLessThan(1000); // 应该在1秒内完成
      expect(result.result).toContain('return h(');
    });
  });
});