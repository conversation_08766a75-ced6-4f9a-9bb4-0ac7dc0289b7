const FuzzyStringMatcher = require('../../../src/ai/tools/FuzzyStringMatcher');

describe('FuzzyStringMatcher - Precision Fix Validation', () => {
  let matcher;

  beforeEach(() => {
    matcher = new FuzzyStringMatcher({ verbose: false });
  });

  describe('位置映射精确性修复验证', () => {
    it('should accurately map positions in Vue renderContent scenario', () => {
      // 模拟真实的 Vue 组件文件片段
      const largeVueFile = `export default {
  data() {
    return {
      loading: false,
      form: { code: '', href: '', icon: '' },
      filterText: '',
      treeData: [],
      defaultProps: { children: 'children', label: 'title' }
    }
  },
  methods: {
    getCheckbox(data) {
      if (this.permissionData.func.length === 0) return
      var multipleSelection = []
      data.forEach((item) => {
        multipleSelection.push(item.relId)
      })
    },
    renderContent(h, { node, data }) {
      // 添加并隐藏id  以便获取DOM时能获取到对应id
      return (
        <div class="connect_id_title">
          {data.title}{' '}
          <span class="connect_id_number" style="opacity:0">
            {data.id}
          </span>
        </div>
      )
    },
    init(uposId) {
      this.uposId = uposId
      if (this.uposId) {
        this.getList()
      }
    }
  }
}`;

      const oldPattern = `renderContent(h, { node, data }) {
      // 添加并隐藏id  以便获取DOM时能获取到对应id
      return (
        <div class="connect_id_title">
          {data.title}{' '}
          <span class="connect_id_number" style="opacity:0">
            {data.id}
          </span>
        </div>
      )
    }`;

      const newReplacement = `renderContent(h, { node, data }) {
      // 添加并隐藏id  以便获取DOM时能获取到对应id
      return h('div', { class: 'connect_id_title' }, [
        h('span', null, data.title),
        h('span', { class: 'connect_id_number', style: { opacity: 0 } }, data.id)
      ])
    }`;

      const result = matcher.smartReplace(largeVueFile, oldPattern, newReplacement);

      expect(result.success).toBe(true);
      expect(result.result).toContain('return h(');
      expect(result.result).toContain('opacity: 0');
      
      // 验证只替换了目标方法，其他部分保持不变
      expect(result.result).toContain('getCheckbox(data)');
      expect(result.result).toContain('init(uposId)');
      expect(result.result).toContain('export default');
      
      // 验证不存在损坏的代码结构
      expect(result.result).not.toContain('return (');
      expect(result.result).not.toContain('<div class="connect_id_title">');
    });

    it('should handle complex indentation without breaking file structure', () => {
      const complexFile = `class Component {
  constructor() {
    this.data = {
      items: []
    }
  }

    renderContent(h, { node, data }) {
      return (
        <div class="item">
          {data.name}
        </div>
      )
    }

  render() {
    return this.items.map(item => this.renderContent(item))
  }
}`;

      const pattern = `renderContent(h, { node, data }) {
      return (
        <div class="item">
          {data.name}
        </div>
      )
    }`;

      const replacement = `renderContent(h, { node, data }) {
      return h('div', { class: 'item' }, data.name)
    }`;

      const result = matcher.smartReplace(complexFile, pattern, replacement);

      expect(result.success).toBe(true);
      expect(result.result).toContain('return h(');
      expect(result.result).toContain('constructor()');
      expect(result.result).toContain('render()');
      expect(result.result).toContain('this.items.map');
    });
  });

  describe('换行符处理统一性验证', () => {
    it('should handle mixed line endings correctly', () => {
      const textWithMixedLineEndings = 'function test() {\r\n  return true;\n}';
      const patternWithMixedLineEndings = 'function test() {\n  return true;\r\n}';
      const replacement = 'function test() {\n  return false;\n}';

      const result = matcher.smartReplace(textWithMixedLineEndings, patternWithMixedLineEndings, replacement);

      expect(result.success).toBe(true);
      expect(result.result).toContain('return false');
    });
  });

  describe('安全检查和错误处理验证', () => {
    it('should validate input parameters', () => {
      const result1 = matcher.smartReplace(null, 'pattern', 'replacement');
      expect(result1.success).toBe(false);
      expect(result1.error).toContain('输入参数无效');

      const result2 = matcher.smartReplace('target', '', 'replacement');
      expect(result2.success).toBe(false);
      expect(result2.error).toContain('输入参数无效');

      const result3 = matcher.smartReplace('target', 'pattern', undefined);
      expect(result3.success).toBe(false);
      expect(result3.error).toContain('输入参数无效');
    });

    it('should detect when replacement did not occur', () => {
      const target = 'function test() { return true; }';
      const pattern = 'function notFound() { return false; }';
      const replacement = 'function replaced() { return null; }';

      const result = matcher.smartReplace(target, pattern, replacement);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });

    it('should handle edge cases gracefully', () => {
      // 测试空字符串替换
      const result1 = matcher.smartReplace('test', 'test', '');
      expect(result1.success).toBe(false);
      expect(result1.error).toContain('替换结果为空');

      // 测试相同内容替换
      const result2 = matcher.smartReplace('test', 'nothing', 'test');
      expect(result2.success).toBe(false);
    });
  });

  describe('精确位置映射验证', () => {
    it('should build precise mapping correctly', () => {
      const original = `  function test() {
    console.log('hello');
      return true;
  }`;
      const normalized = `function test() {
console.log('hello');
return true;
}`;

      // 测试 buildPreciseMapping 方法
      const result = matcher.buildPreciseMapping(original, normalized, 
        normalized.indexOf('console.log'), 'console.log'.length);

      expect(result.index).toBe(original.indexOf('console.log'));
      expect(result.text).toBe('console.log');
    });

    it('should handle whitespace-heavy content accurately', () => {
      const original = `\n\n  \t  function example() {\n    \t  var x = 1;\n  \t  return x;\n  }\n\n`;
      const normalized = `function example() {\nvar x = 1;\nreturn x;\n}`;

      const targetIndex = normalized.indexOf('var x = 1');
      const result = matcher.buildPreciseMapping(original, normalized, targetIndex, 'var x = 1'.length);

      expect(result.index).toBe(original.indexOf('var x = 1'));
      expect(result.text).toContain('var x = 1');
    });
  });

  describe('真实场景压力测试', () => {
    it('should handle very large Vue component files', () => {
      // 创建一个大型 Vue 组件文件
      let largeComponent = `<template>\n${'  <div>Large content</div>\n'.repeat(500)}</template>\n\n`;
      largeComponent += `<script>\nexport default {\n  data() {\n    return {\n`;
      largeComponent += `${'      property: "value",\n'.repeat(100)}`;
      largeComponent += `    }\n  },\n  methods: {\n`;
      largeComponent += `${'    method() { console.log("test"); },\n'.repeat(50)}`;
      largeComponent += `    renderContent(h, { node, data }) {\n      return <div>{data.title}</div>\n    }\n  }\n}\n</script>`;

      const pattern = `renderContent(h, { node, data }) {
      return <div>{data.title}</div>
    }`;

      const replacement = `renderContent(h, { node, data }) {
      return h('div', null, data.title)
    }`;

      const start = Date.now();
      const result = matcher.smartReplace(largeComponent, pattern, replacement);
      const duration = Date.now() - start;

      expect(result.success).toBe(true);
      expect(result.result).toContain('return h(');
      expect(duration).toBeLessThan(1000); // 应该在1秒内完成
      
      // 验证文件结构完整性
      expect(result.result).toContain('<template>');
      expect(result.result).toContain('<script>');
      expect(result.result).toContain('export default');
    });

    it('should maintain precision with multiple similar methods', () => {
      const fileWithSimilarMethods = `class Component {
  renderContent(h, { node, data }) {
    return <div class="target">{data.title}</div>
  }

  renderOtherContent(h, { node, data }) {
    return <span class="other">{data.title}</span>
  }

  renderContentWrapper() {
    return this.renderContent()
  }

  renderContent2(h, { node, data }) {
    return <p class="target2">{data.title}</p>
  }
}`;

      const pattern = `renderContent(h, { node, data }) {
    return <div class="target">{data.title}</div>
  }`;

      const replacement = `renderContent(h, { node, data }) {
    return h('div', { class: 'target' }, data.title)
  }`;

      const result = matcher.smartReplace(fileWithSimilarMethods, pattern, replacement);

      expect(result.success).toBe(true);
      expect(result.result).toContain('return h(');
      
      // 验证只替换了目标方法
      expect(result.result).toContain('renderOtherContent');
      expect(result.result).toContain('renderContentWrapper');
      expect(result.result).toContain('renderContent2');
      expect(result.result).toContain('<span class="other">');
      expect(result.result).toContain('<p class="target2">');
    });
  });
});