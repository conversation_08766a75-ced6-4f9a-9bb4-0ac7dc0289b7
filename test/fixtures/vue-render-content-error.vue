<template>
  <div>
    <el-tree
      ref="tree"
      class="filter-tree"
      :data="treeData"
      node-key="id"
      highlight-current
      :props="defaultProps"
      :render-content="renderContent"
      @node-click="handleNodeClick"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      treeData: [
        {
          id: '1',
          title: 'Node 1',
          children: [
            {
              id: '1-1',
              title: 'Node 1-1'
            }
          ]
        },
        {
          id: '2',
          title: 'Node 2'
        }
      ],
      defaultProps: {
        children: 'children',
        label: 'title'
      }
    }
  },
  methods: {
    renderContent(h, { node, data }) {
      // This is the function with Vue 2 syntax that needs to be migrated
      return (
        <div class="connect_id_title">
          {data.title}{' '}
          <span class="connect_id_number" style="opacity:0">
            {data.id}
          </span>
        </div>
      )
    },
    handleNodeClick(data) {
      console.log('Node clicked:', data)
    }
  }
}
</script>

<style>
.connect_id_title {
  display: flex;
  align-items: center;
}
.connect_id_number {
  margin-left: 5px;
  font-size: 12px;
  color: #999;
}
</style>
