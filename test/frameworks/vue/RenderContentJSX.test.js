const fs = require('fs-extra');
const path = require('path');
const RenderFunctionTransformer = require('../../../src/frameworks/vue/RenderFunctionTransformer');

describe('RenderFunctionTransformer with JSX', () => {
  const transformer = new RenderFunctionTransformer({ verbose: true });

  it('should detect renderContent with JSX in Vue template', async () => {
    const vueCode = `
<template>
  <div>
    <el-tree
      :data="treeData"
      node-key="id"
      :render-content="renderContent"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      treeData: []
    }
  },
  methods: {
    renderContent(h, { node, data }) { // 添加并隐藏id  以便获取DOM时能获取到对应id
      return (
        <div class='connect_id_title'>
          {data.title} <span class='connect_id_number' style='opacity:0'>{data.id}</span>
        </div>
      )
    }
  }
}
</script>
`;

    // Check if the transformer detects renderContent
    expect(transformer.hasRenderContent(vueCode)).toBe(true);

    // Transform the code
    const result = await transformer.transform(vueCode, 'test.vue');
    
    // Verify transformation was successful
    expect(result).not.toEqual(vueCode);
    expect(result).toContain("import { h } from 'vue'");
    expect(result).toContain("renderContent({ node, data })");
    expect(result).not.toContain("renderContent(h, { node, data })");
  });

  it('should detect complex renderContent with nested JSX', async () => {
    const vueCode = `
<template>
  <div>
    <el-tree
      id="column-tree"
      ref="menuTree"
      class="filter-tree"
      :data="treeData"
      node-key="id"
      highlight-current
      :props="defaultProps"
      :filter-node-method="filterNode"
      default-expand-all
      show-checkbox
      :render-content="renderContent"
      @node-click="getNodeData"
      @check-change="checkChange"
      @check="nodeClick"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      treeData: [],
      defaultProps: {
        children: 'children',
        label: 'title'
      }
    }
  },
  methods: {
    renderContent(h, { node, data }) {
      return (
        <div class='connect_id_title'>
          {data.title} <span class='connect_id_number' style='opacity:0'>{data.id}</span>
          <div class="nested">
            <span>{node.label}</span>
          </div>
        </div>
      )
    },
    filterNode() {},
    getNodeData() {},
    checkChange() {},
    nodeClick() {}
  }
}
</script>
`;

    // Check if the transformer detects renderContent
    expect(transformer.hasRenderContent(vueCode)).toBe(true);

    // Transform the code
    const result = await transformer.transform(vueCode, 'test.vue');
    
    // Verify transformation was successful
    expect(result).not.toEqual(vueCode);
    expect(result).toContain("import { h } from 'vue'");
    expect(result).toContain("renderContent({ node, data })");
    expect(result).not.toContain("renderContent(h, { node, data })");
  });

  it('should handle real-world complex Vue component', async () => {
    // This is a simplified version of the user's real code
    const vueCode = `
<template>
  <div>
    <el-tree
      id="column-tree"
      ref="menuTree"
      class="filter-tree"
      :data="treeData"
      node-key="id"
      highlight-current
      :props="defaultProps"
      :filter-node-method="filterNode"
      default-expand-all
      show-checkbox
      :render-content="renderContent"
      @node-click="getNodeData"
    />
  </div>
</template>

<script>
export default {
  data() {
    return {
      treeData: [],
      defaultProps: {
        children: 'children',
        label: 'title'
      }
    }
  },
  methods: {
    renderContent(h, { node, data }) { // 添加并隐藏id  以便获取DOM时能获取到对应id
      return (
        <div class='connect_id_title'>
          {data.title} <span class='connect_id_number' style='opacity:0'>{data.id}</span>
        </div>
      )
    },
    filterNode(value, data) {
      if (!value) return true
      return data.title.indexOf(value) !== -1
    },
    getNodeData(data) {
      // Some implementation
    }
  }
}
</script>
`;

    // Check if the transformer detects renderContent
    expect(transformer.hasRenderContent(vueCode)).toBe(true);

    // Transform the code
    const result = await transformer.transform(vueCode, 'test.vue');
    
    // Verify transformation was successful
    expect(result).not.toEqual(vueCode);
    expect(result).toContain("import { h } from 'vue'");
    expect(result).toContain("renderContent({ node, data })");
    expect(result).not.toContain("renderContent(h, { node, data })");
  });
});
