const RenderFunctionTransformer = require('../../../src/frameworks/vue/RenderFunctionTransformer')

describe('RenderFunctionTransformer - Export Default Function Render', () => {
	let transformer

	beforeEach(() => {
		transformer = new RenderFunctionTransformer({ verbose: false })
	})

	test('should detect export default function render format', () => {
		const code = `
      <script>
      export default function render(_props, _context) {
        return <div>Hello</div>
      }
      </script>
    `

		expect(transformer.isExportDefaultRenderFunction(code)).toBe(true)
	})

	test('should not detect regular export default', () => {
		const code = `
      <script>
      export default {
        name: 'Component'
      }
      </script>
    `

		expect(transformer.isExportDefaultRenderFunction(code)).toBe(false)
	})

	test('should transform export default function render to setup function', async () => {
		const code = `
      <script>
      import * as Vue from 'vue'
      export default function render(_props, _context) {
        const context = {
          ..._context,
          props: _props,
          data: _context.attr,
          children: _context.slots,
        }
        const { icon, title } = context.props
        const vnodes = []

        if (icon) {
          vnodes.push(<svg-icon icon-class={icon} />)
        }

        if (title) {
          vnodes.push(<span slot="title">{title}</span>)
        }
        return vnodes
      }
      </script>
    `

		const result = await transformer.transform(code, 'test.vue')

		expect(result).toContain('setup(props, { slots, emit, attrs })')
		expect(result).toContain('const { icon, title } = props')
		expect(result).toContain('h(\'svg-icon\', { "icon-class": icon })')
		expect(result).toContain('h(\'span\', { "slot": "title" }, title)')
		expect(result).toContain('import { h } from \'vue\'')
		expect(result).not.toContain('context = {')
		expect(result).not.toContain('_props')
		expect(result).not.toContain('_context')
	})

	test('should handle complex JSX expressions', async () => {
const code = `
<script>
export default function render(_props, _context) {
  const context = { ..._context, props: _props }
  const { onClick, disabled } = context.props
  
  return (
    <div class="menu-item" onClick={onClick}>
      <span>Content</span>
    </div>
  )
}
</script>
`

		const result = await transformer.transform(code, 'test.vue')

		// For complex JSX that might not be fully converted, just check for basic transformation
		expect(result).toContain('const { onClick, disabled } = props')
		expect(result).toContain('setup(props, { slots, emit, attrs })')
		expect(result).toContain('import { h } from \'vue\'')
	})

	test('should handle self-closing JSX tags', async () => {
		const code = `
      <script>
      export default function render(_props, _context) {
        const { icon } = _props
        return <svg-icon icon-class={icon} size="large" />
      }
      </script>
    `

		const result = await transformer.transform(code, 'test.vue')

		expect(result).toContain('h(\'svg-icon\', { "icon-class": icon, "size": "large" })')
	})

	test('should preserve JSX attribute names with dashes', async () => {
		const code = `
      <script>
      export default function render(_props, _context) {
        return <custom-component my-prop={value} data-id="test" />
      }
      </script>
    `

		const result = await transformer.transform(code, 'test.vue')

		expect(result).toContain('"my-prop": value')
		expect(result).toContain('"data-id": "test"')
	})
})
