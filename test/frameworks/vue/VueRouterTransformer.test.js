const VueRouterTransformer = require('../../../src/frameworks/vue/VueRouterTransformer');

describe('VueRouterTransformer', () => {
  let transformer;

  beforeEach(() => {
    transformer = new VueRouterTransformer({
      verbose: false
    });
  });

  describe('hasVueRouterContent', () => {
    it('should detect router.addRoutes() calls', () => {
      const code = `
        router.addRoutes(accessRoutes)
      `;
      expect(transformer.hasVueRouterContent(code)).toBe(true);
    });

    it('should detect addRoutes with different router names', () => {
      const code = `
        myRouter.addRoutes(routes)
      `;
      expect(transformer.hasVueRouterContent(code)).toBe(true);
    });

    it('should not detect when no addRoutes calls exist', () => {
      const code = `
        router.push('/home')
        router.replace('/login')
      `;
      expect(transformer.hasVueRouterContent(code)).toBe(false);
    });
  });

  describe('transform - basic functionality', () => {
    it('should transform router.addRoutes() to forEach loop', async () => {
      const input = `
        router.addRoutes(accessRoutes)
      `;

      const result = await transformer.transform(input);

      // Check that the transformation contains the expected elements
      expect(result).toContain('accessRoutes.forEach(route => {');
      expect(result).toContain('router.addRoute(route)');
      expect(result).toContain('})');
      expect(result).not.toContain('router.addRoutes(accessRoutes)');
    });

    it('should transform with different router variable names', async () => {
      const input = `
        myRouter.addRoutes(dynamicRoutes)
      `;

      const result = await transformer.transform(input);

      expect(result).toContain('dynamicRoutes.forEach(route => {');
      expect(result).toContain('myRouter.addRoute(route)');
      expect(result).toContain('})');
      expect(result).not.toContain('myRouter.addRoutes(dynamicRoutes)');
    });

    it('should handle complex route expressions', async () => {
      const input = `
        router.addRoutes(store.getters.accessRoutes)
      `;

      const result = await transformer.transform(input);

      expect(result).toContain('store.getters.accessRoutes.forEach(route => {');
      expect(result).toContain('router.addRoute(route)');
      expect(result).toContain('})');
      expect(result).not.toContain('router.addRoutes(store.getters.accessRoutes)');
    });
  });

  describe('transform - real-world scenarios', () => {
    it('should transform permission.js style code', async () => {
      const input = `
import router from './router'
import { resetRouter } from '@/router'
import store from './store'

router.beforeEach(async (to, from, next) => {
  const hasToken = getToken()
  
  if (hasToken) {
    if (store.getters.status === 0) {
      try {
        const accessRoutes = await store.dispatch('permission/generateRoutes', { navmenus })
        
        resetRouter()
        router.addRoutes(accessRoutes)
        next({ ...to, replace: true })
      } catch (error) {
        console.error(error)
      }
    }
  }
})
      `;

      const result = await transformer.transform(input);
      
      expect(result).toContain('accessRoutes.forEach(route => {');
      expect(result).toContain('router.addRoute(route)');
      expect(result).toContain('})');
      expect(result).not.toContain('router.addRoutes(accessRoutes)');
    });

    it('should handle multiple addRoutes calls in same file', async () => {
      const input = `
        const routes1 = getRoutes1()
        const routes2 = getRoutes2()
        
        router.addRoutes(routes1)
        router.addRoutes(routes2)
      `;

      const result = await transformer.transform(input);
      
      expect(result).toContain('routes1.forEach(route => {');
      expect(result).toContain('routes2.forEach(route => {');
      expect((result.match(/router\.addRoute\(route\)/g) || []).length).toBe(2);
    });

    it('should preserve other router methods', async () => {
      const input = `
        router.push('/home')
        router.addRoutes(accessRoutes)
        router.replace('/login')
      `;

      const result = await transformer.transform(input);
      
      expect(result).toContain('router.push(\'/home\')');
      expect(result).toContain('router.replace(\'/login\')');
      expect(result).toContain('accessRoutes.forEach(route => {');
      expect(result).toContain('router.addRoute(route)');
    });
  });

  describe('transform - edge cases', () => {
    it('should handle addRoutes with whitespace', async () => {
      const input = `
        router.addRoutes  (  accessRoutes  )
      `;
      
      const result = await transformer.transform(input);
      expect(result).toContain('accessRoutes.forEach(route => {');
      expect(result).toContain('router.addRoute(route)');
    });

    it('should handle nested function calls', async () => {
      const input = `
        router.addRoutes(generateRoutes(userPermissions))
      `;
      
      const result = await transformer.transform(input);
      expect(result).toContain('generateRoutes(userPermissions).forEach(route => {');
      expect(result).toContain('router.addRoute(route)');
    });

    it('should return original code when no transformation needed', async () => {
      const input = `
        router.push('/home')
        const routes = getRoutes()
      `;
      
      const result = await transformer.transform(input);
      expect(result).toBe(input);
    });

    it('should handle syntax errors gracefully', async () => {
      const input = `
        router.addRoutes(accessRoutes
      `;
      
      const result = await transformer.transform(input);
      // Should fallback to regex transformation or return original
      expect(typeof result).toBe('string');
    });
  });

  describe('statistics', () => {
    it('should track successful transformations', async () => {
      const input = `router.addRoutes(accessRoutes)`;
      
      await transformer.transform(input);
      
      const stats = transformer.getStats();
      expect(stats.success).toBe(1);
      expect(stats.failed).toBe(0);
      expect(stats.total).toBe(1);
      expect(stats.transformations).toHaveLength(1);
    });

    it('should track failed transformations', async () => {
      // Mock a scenario that would cause failure
      const originalTransformWithAST = transformer.transformWithAST;
      transformer.transformWithAST = jest.fn().mockRejectedValue(new Error('Test error'));
      
      const input = `router.addRoutes(accessRoutes)`;
      await transformer.transform(input);
      
      const stats = transformer.getStats();
      expect(stats.failed).toBe(1);
      
      // Restore original method
      transformer.transformWithAST = originalTransformWithAST;
    });

    it('should reset statistics', async () => {
      const input = `router.addRoutes(accessRoutes)`;
      await transformer.transform(input);
      
      transformer.resetStats();
      
      const stats = transformer.getStats();
      expect(stats.success).toBe(0);
      expect(stats.failed).toBe(0);
      expect(stats.transformations).toHaveLength(0);
    });
  });

  describe('regex fallback', () => {
    it('should use regex transformation when AST fails', () => {
      const input = `router.addRoutes(accessRoutes)`;
      
      const result = transformer.transformWithRegex(input, 'test.js');
      
      expect(result.code).toContain('accessRoutes.forEach(route => {');
      expect(result.code).toContain('router.addRoute(route)');
      expect(result.changes).toHaveLength(1);
      expect(result.changes[0].type).toBe('addRoutes-to-forEach');
    });
  });
});
