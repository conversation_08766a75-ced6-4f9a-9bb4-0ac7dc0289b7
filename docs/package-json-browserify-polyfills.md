# Package.json Browserify Polyfills 自动添加功能

## 概述

在 Vue 2 到 Vue 3 的迁移过程中，某些 Node.js 模块（如 `path` 和 `stream`）在浏览器环境中不可用。为了解决这个问题，PackageJsonMerger 现在会自动添加必要的 browserify polyfills。

## 自动添加的依赖

PackageJsonMerger 会自动添加以下 browserify polyfill 依赖：

### 1. path-browserify
- **版本**: `^1.0.1`
- **用途**: 为浏览器环境提供 Node.js `path` 模块的功能
- **说明**: 允许在前端代码中使用 `path.join()`, `path.resolve()` 等方法

### 2. stream-browserify
- **版本**: `^3.0.0`
- **用途**: 为浏览器环境提供 Node.js `stream` 模块的功能
- **说明**: 支持在浏览器中使用流处理功能

## 工作原理

### 设计理念

这些 browserify polyfills **不是基于源项目依赖检测的映射**，而是 Vue 3 迁移过程中**必需的依赖**。因此，它们不在 `package-recommend.json` 的 `knownCompatible` 配置中，而是直接在代码中定义。

### 自动添加逻辑

1. **必需依赖**: 这些 polyfills 在每次 Vue 3 迁移时都会被添加，不依赖于源项目是否使用了相关模块
2. **检查现有依赖**: 首先检查目标项目是否已经包含这些 polyfill
3. **避免覆盖**: 如果已存在，保留现有版本，不进行覆盖
4. **创建依赖部分**: 如果目标项目没有 `dependencies` 部分，会自动创建
5. **添加到 dependencies**: 将 polyfills 添加到 `dependencies` 而不是 `devDependencies`

### 为什么不在配置文件中？

- `knownCompatible` 用于源项目依赖的兼容性映射
- `knownIncompatible` 用于不兼容依赖的替换建议
- `needsUpgrade` 用于需要升级版本的依赖
- **browserify polyfills 是迁移过程中的必需依赖**，不属于以上任何类别

## 使用示例

### 基本使用

```javascript
const PackageJsonMerger = require('./src/features/package-json/PackageJsonMerger');

const merger = new PackageJsonMerger(sourceProjectPath, targetProjectPath, {
  preserveTargetDependencies: true,
  enableThirdPartyMapping: true,
  verbose: true
});

const result = await merger.merge();

// 检查是否添加了 browserify polyfills
const deps = result.mergedPackageJson.dependencies;
console.log('path-browserify:', deps['path-browserify']); // ^1.0.1
console.log('stream-browserify:', deps['stream-browserify']); // ^3.0.0
```

### 在迁移工具中的集成

这个功能已经集成到 AutoMigrator 中，在执行 package.json 合并步骤时会自动添加这些 polyfills：

```javascript
// 在 AutoMigrator.js 中
await this.packageJsonMigrator.migrate();
// 此时会自动添加 browserify polyfills
```

## 配置选项

### 禁用自动添加

如果需要禁用自动添加 browserify polyfills，可以通过修改 PackageJsonMerger 的逻辑或在配置中添加相应选项。

### 自定义版本

如果需要使用不同版本的 polyfills，可以：

1. 在目标项目中预先添加所需版本
2. 修改 `config/package-recommend.json` 中的版本号

## 测试

功能包含完整的单元测试，覆盖以下场景：

- ✅ 自动添加 polyfills
- ✅ 不覆盖现有 polyfills
- ✅ 处理缺失的 dependencies 部分
- ✅ 与其他依赖映射功能的兼容性

运行测试：

```bash
npm test -- test/features/package-json/PackageJsonMerger.test.js
```

## 注意事项

1. **版本兼容性**: 选择的版本经过测试，与 Vue 3 和现代构建工具兼容
2. **构建配置**: 可能需要在 webpack 或 vite 配置中添加相应的 alias 配置
3. **性能影响**: 这些 polyfills 会增加打包体积，但通常影响很小

## 相关文件

- `src/features/package-json/PackageJsonMerger.js` - 主要实现
- `config/package-recommend.json` - 配置文件
- `test/features/package-json/PackageJsonMerger.test.js` - 单元测试
- `examples/package-json-merger-example.js` - 使用示例
