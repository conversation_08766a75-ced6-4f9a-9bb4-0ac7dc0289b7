{"compilerOptions": {"target": "ES2020", "module": "commonjs", "moduleResolution": "node", "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": true, "skipLibCheck": true, "outDir": "dist", "declaration": true, "sourceMap": true, "baseUrl": ".", "paths": {"*": ["node_modules/*"], "@/*": ["src/*"]}}, "include": ["src/**/*", "bin/**/*", "index.ts"], "exclude": ["node_modules", "dist"]}