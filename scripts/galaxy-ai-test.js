#!/usr/bin/env node

require('dotenv').config();

const { Command } = require('commander');
const chalk = require('chalk');
const { GalaxyAiService } = require('../src/ai/GalaxyAiService');

const program = new Command();

program
  .name('galaxy-ai-test')
  .description('Galaxy AI Service 连接测试工具')
  .version('1.0.0');

program
  .command('test-connection')
  .description('🔗 测试与 Galaxy AI 平台的连接')
  .option('--verbose', '显示详细信息')
  .action(async (options) => {
    try {
      console.log(chalk.blue('🚀 开始测试 Galaxy AI 服务连接...'));

      // Check environment variables
      const requiredEnvVars = ['GALAXY_SYSTEM_ID', 'GALAXY_SYSTEM_SECRET'];
      const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

      if (missingVars.length > 0) {
        console.log(chalk.red('❌ 缺少必要的环境变量:'));
        missingVars.forEach(varName => {
          console.log(chalk.red(`   - ${varName}`));
        });
        console.log(chalk.yellow('\n请在 .env 文件中设置以下变量:'));
        console.log(chalk.gray('GALAXY_SYSTEM_ID=your-system-id'));
        console.log(chalk.gray('GALAXY_SYSTEM_SECRET=your-system-secret'));
        console.log(chalk.gray('GALAXY_ACCOUNT=your-account (可选)'));
        console.log(chalk.gray('GALAXY_APP_ID=your-app-id (可选)'));
        console.log(chalk.gray('GALAXY_BASE_URL=http://copilot.prodgpu.chinastock.com.cn (可选)'));
        process.exit(1);
      }

      if (options.verbose) {
        console.log(chalk.gray('🔍 环境变量检查:'));
        console.log(chalk.gray(`   GALAXY_SYSTEM_ID: ${process.env.GALAXY_SYSTEM_ID ? '✓' : '✗'}`));
        console.log(chalk.gray(`   GALAXY_SYSTEM_SECRET: ${process.env.GALAXY_SYSTEM_SECRET ? '✓' : '✗'}`));
        console.log(chalk.gray(`   GALAXY_ACCOUNT: ${process.env.GALAXY_ACCOUNT || '未设置'}`));
        console.log(chalk.gray(`   GALAXY_APP_ID: ${process.env.GALAXY_APP_ID || '未设置'}`));
        console.log(chalk.gray(`   GALAXY_BASE_URL: ${process.env.GALAXY_BASE_URL || '使用默认值'}`));
      }

      // Initialize Galaxy AI Service
      const galaxyAI = new GalaxyAiService();

      console.log(chalk.yellow('🔑 正在获取访问令牌...'));

      // Test token acquisition
      const token = await galaxyAI.getSystemToken();

      if (token) {
        console.log(chalk.green('✅ 成功获取访问令牌'));
        if (options.verbose) {
          console.log(chalk.gray(`   Token: ${token.substring(0, 20)}...`));
        }
      } else {
        throw new Error('未能获取访问令牌');
      }

      console.log(chalk.green('🎉 Galaxy AI 服务连接测试成功!'));

    } catch (error) {
      console.log(chalk.red('❌ Galaxy AI 服务连接测试失败:'));
      console.log(chalk.red(`   ${error.message}`));

      if (options.verbose && error.stack) {
        console.log(chalk.gray('\n详细错误信息:'));
        console.log(chalk.gray(error.stack));
      }

      process.exit(1);
    }
  });

program
  .command('test-chat')
  .description('💬 测试 Galaxy AI 聊天功能')
  .option('--message <message>', '测试消息', '你好，请介绍一下你自己')
  .option('--verbose', '显示详细信息')
  .action(async (options) => {
    // try {
      console.log(chalk.blue('🚀 开始测试 Galaxy AI 聊天功能...'));

      const galaxyAI = new GalaxyAiService();

      console.log(chalk.yellow('🔑 正在获取访问令牌...'));
      await galaxyAI.getSystemToken();
      console.log(chalk.green('✅ 访问令牌获取成功'));

      console.log(chalk.yellow(`💬 发送测试消息: "${options.message}"`));

      const result = await galaxyAI.generateText({
        prompt: options.message,
        conversationId: `test-${Date.now()}`
      });

      console.log(chalk.green('✅ 聊天测试成功!'));
      console.log(chalk.blue('🤖 AI 回复:'));
      console.log(chalk.white(result.text));

      if (options.verbose) {
        console.log(chalk.gray('\n详细信息:'));
        console.log(chalk.gray(`   完成原因: ${result.finishReason}`));
        console.log(chalk.gray(`   令牌使用: ${JSON.stringify(result.usage)}`));
      }
    //
    // } catch (error) {
    //   console.log(chalk.red('❌ Galaxy AI 聊天测试失败:'));
    //   console.log(chalk.red(`   ${error.message}`));
    //
    //   if (options.verbose && error.stack) {
    //     console.log(chalk.gray('\n详细错误信息:'));
    //     console.log(chalk.gray(error.stack));
    //   }
    //
    //   process.exit(1);
    // }
  });

program
  .command('test-ai-sdk-compatibility')
  .description('🔄 测试与 AI.js SDK 的兼容性')
  .option('--verbose', '显示详细信息')
  .action(async (options) => {
    try {
      console.log(chalk.blue('🚀 开始测试 AI.js SDK 兼容性...'));

      const galaxyAI = new GalaxyAiService();

      // Test AI.js SDK compatible interface
      const testCases = [
        {
          name: '简单文本生成',
          options: {
            prompt: '请用一句话介绍人工智能',
          }
        },
        {
          name: '消息格式测试',
          options: {
            messages: [
              { role: 'user', content: '你好' },
              { role: 'assistant', content: '你好！有什么可以帮助你的吗？' },
              { role: 'user', content: '请介绍一下你的功能' }
            ]
          }
        }
      ];

      for (const testCase of testCases) {
        console.log(chalk.yellow(`🧪 测试: ${testCase.name}`));

        const result = await galaxyAI.generateText(testCase.options);

        // Validate AI.js SDK compatible response format
        if (typeof result.text !== 'string') {
          throw new Error(`响应格式错误: text 应该是字符串，实际是 ${typeof result.text}`);
        }

        if (!result.finishReason) {
          throw new Error('响应格式错误: 缺少 finishReason');
        }

        if (!result.usage || typeof result.usage !== 'object') {
          throw new Error('响应格式错误: 缺少或格式错误的 usage 对象');
        }

        console.log(chalk.green(`   ✅ ${testCase.name} 通过`));

        if (options.verbose) {
          console.log(chalk.gray(`   响应: ${result.text.substring(0, 100)}${result.text.length > 100 ? '...' : ''}`));
        }
      }

      console.log(chalk.green('🎉 AI.js SDK 兼容性测试全部通过!'));

    } catch (error) {
      console.log(chalk.red('❌ AI.js SDK 兼容性测试失败:'));
      console.log(chalk.red(`   ${error.message}`));

      if (options.verbose && error.stack) {
        console.log(chalk.gray('\n详细错误信息:'));
        console.log(chalk.gray(error.stack));
      }

      process.exit(1);
    }
  });

program.parse();
