#!/usr/bin/env node

require('dotenv').config();

const { Command } = require('commander');
const chalk = require('chalk');
const path = require('path');
const fs = require('fs-extra');
const PackageJsonMigrator = require('../src/app/PackageJsonMigrator');

const program = new Command();

program
  .name('package-json-migrator-test')
  .description('PackageJsonMigrator 测试工具')
  .version('1.0.0');

program
  .command('migrate')
  .description('⚡ 使用指定的测试项目快速测试')
  .option('--test-project <path>', '测试项目路径', '/Users/<USER>/works/galaxy/aup-admin-ui')
  .option('--dry-run', '预览模式，不实际修改文件')
  .option('--verbose', '显示详细信息')
  .action(async (options) => {
    try {
      const testProjectPath = options.testProject;

      console.log(chalk.blue('⚡ 开始快速测试...'));
      console.log(chalk.gray(`使用测试项目: ${testProjectPath}`));

      // 检查测试项目是否存在
      if (!await fs.pathExists(testProjectPath)) {
        throw new Error(`测试项目路径不存在: ${testProjectPath}\n请确保路径正确或使用 --test-project 指定其他路径`);
      }

      // 先验证项目
      console.log(chalk.yellow('\n🔍 第一步：验证项目...'));
      await validateProject(testProjectPath, options.verbose);

      // 然后测试单项目模式
      console.log(chalk.yellow('\n🔧 第二步：测试单项目模式迁移...'));
      await testSingleMode(testProjectPath, {
        dryRun: options.dryRun,
        verbose: options.verbose,
        migrationMode: true
      });

      console.log(chalk.green('\n🎉 快速测试完成！'));

    } catch (error) {
      console.error(chalk.red('\n❌ 快速测试失败:'), error.message);
      if (options.verbose && error.stack) {
        console.error(chalk.gray('\n详细错误信息:'));
        console.error(chalk.gray(error.stack));
      }
      process.exit(1);
    }
  });

// 辅助函数：验证项目
async function validateProject(projectPath, verbose = false) {
  const packageJsonPath = path.join(projectPath, 'package.json');
  if (!await fs.pathExists(packageJsonPath)) {
    throw new Error(`项目中未找到 package.json: ${packageJsonPath}`);
  }

  const packageJson = await fs.readJson(packageJsonPath);

  console.log(chalk.gray(`  项目名称: ${packageJson.name || '未设置'}`));

  // 检查 Vue 版本
  const vueVersion = packageJson.dependencies?.vue || packageJson.devDependencies?.vue;
  if (vueVersion) {
    console.log(chalk.gray(`  Vue 版本: ${vueVersion}`));
  } else {
    console.log(chalk.yellow('  ⚠️  未检测到 Vue 依赖'));
  }

  return packageJson;
}

// 辅助函数：测试单项目模式
async function testSingleMode(projectPath, options = {}) {
  const migrator = new PackageJsonMigrator({
    workingPath: projectPath,
    sourceToTargetMode: false,
    migrationMode: options.migrationMode || false,
    preserveVue3Dependencies: true,
    enableThirdPartyMapping: true,
    verbose: options.verbose || false,
    dryRun: options.dryRun || false
  });

  const results = await migrator.processPackageJson();

  if (options.verbose) {
    migrator.printMigrationReport(results);
  } else {
    console.log(chalk.gray(`  总变更数: ${results.totalChanges}`));
  }

  return results;
}

program.parse();
