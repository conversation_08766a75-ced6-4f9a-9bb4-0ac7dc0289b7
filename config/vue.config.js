const path = require('path')


function resolve(dir) {
  return path.join(__dirname, dir)
}

module.exports = {
  configureWebpack: {
    name: "Name",
    resolve: {
      alias: {
        '@': resolve('src/'),
        // todo: add resolve for gogoTransfer in future
      },
      fallback: {
        // 面向 Vue 2 迁移的 fallback 配置
        // Module not found: Error: Can't resolve 'path' in '...'
        // Module not found: Error: Can't resolve 'stream' in '...'
        path: require.resolve('path-browserify'),
        stream: require.resolve('stream-browserify'),
      },
      extensions: ['.js', '.ts', '.vue', '.json'],
    },
    optimization: {
      splitChunks: {
        cacheGroups: {
          default: false,
          libs: {
            name: 'chunk-libs',
            test: /[\\/]node_modules[\\/]/,
            priority: 10,
            chunks: 'initial',
          },
          elementPlus: {
            name: 'chunk-element-plus',
            priority: 20,
            test: /[\\/]node_modules[\\/]_?element-plus(.*)/,
          },
          commons: {
            name: 'chunk-commons',
            test: resolve('src/components'),
            minChunks: 3,
            priority: 5,
            reuseExistingChunk: true,
          },
        },
      },
      runtimeChunk: 'single',
    },
  },
  chainWebpack: config => {
    const PreloadPlugin = require('@vue/preload-webpack-plugin')
    config.plugin('preload').use(new PreloadPlugin({
      rel: 'preload',
      fileBlacklist: [/\.map$/, /hot-update\.js$/, /runtime\..*\.js$/],
      include: 'initial',
    }))

    config.plugins.delete('prefetch')
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]',
      })
      .end()
  },
  publicPath: '/',
  outputDir: 'dist',
  assetsDir: 'static',
  lintOnSave: process.env.NODE_ENV === 'development',
  productionSourceMap: false,
  devServer: {
    port: process.env.port || process.env.npm_config_port || 9527,
    open: true,
    setupMiddlewares(middlewares, devServer) {
      const mockServer = require('./mock/mock-server.js')
      mockServer(devServer.app)
      return middlewares
    },
  },
}
