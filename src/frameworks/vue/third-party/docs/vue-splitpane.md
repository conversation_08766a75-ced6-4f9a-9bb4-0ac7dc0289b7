---
source: vue-splitpane
target: splitpanes
link: https://github.com/antoniandre/splitpanes
---

# 从 vue-splitpane 迁移到 splitpanes

`splitpanes` 是一个功能强大、可靠、简洁且支持触摸操作的 Vue.js 窗格分割/调整器，兼容 Vue 3 和 Vue 2。本文档将指导你如何从 `vue-splitpane` 迁移到 `splitpanes`。

## 主要区别

- **组件化**: `splitpanes` 采用更现代的组件化 API，使用 `<splitpanes>` 作为容器，`<pane>` 作为独立的窗格组件。
- **功能更丰富**: `splitpanes` 提供了更多功能，如水平/垂直分割、RTL 支持、事件监听、窗格最小/最大尺寸限制等。
- **Vue 3 支持**: `splitpanes` 为 Vue 3 提供了原生支持。

## 安装

首先，你需要从你的项目中移除 `vue-splitpane`，然后安装 `splitpanes`。

```bash
npm uninstall vue-splitpane
npm install splitpanes
```

或者使用 yarn:

```bash
yarn remove vue-splitpane
yarn add splitpanes
```

## 使用方法

### 1. 导入组件和样式

在你的 Vue 组件中，你需要导入 `Splitpanes` 和 `Pane` 组件，以及它的 CSS 文件。

```vue
<script setup>
import { Splitpanes, Pane } from 'splitpanes'
import 'splitpanes/dist/splitpanes.css'
</script>
```

### 2. 基本用法

`vue-splitpane` 通常像下面这样使用：

```html
<!-- vue-splitpane 示例 -->
<split-pane :min-percent='20' :default-percent='30' split="vertical">
  <template slot="paneL">A</template>
  <template slot="paneR">B</template>
</split-pane>
```

使用 `splitpanes`，你可以这样实现：

```vue
<template>
  <splitpanes style="height: 400px">
    <pane min-size="20" size="30">
      <span>A</span>
    </pane>
    <pane>
      <span>B</span>
    </pane>
  </splitpanes>
</template>

<script setup>
import { Splitpanes, Pane } from 'splitpanes'
import 'splitpanes/dist/splitpanes.css'
</script>
```

### 3. 水平分割

在 `splitpanes` 中，你可以使用 `horizontal` 属性来实现水平分割：

```vue
<template>
  <splitpanes horizontal style="height: 400px">
    <pane min-size="20">
      <span>A</span>
    </pane>
    <pane>
      <span>B</span>
    </pane>
    <pane>
      <span>C</span>
    </pane>
  </splitpanes>
</template>

<script setup>
import { Splitpanes, Pane } from 'splitpanes'
import 'splitpanes/dist/splitpanes.css'
</script>
```

### 4. 嵌套使用

`splitpanes` 支持嵌套，可以创建复杂的布局：

```vue
<template>
  <splitpanes style="height: 400px">
    <pane>
      <span>1</span>
    </pane>
    <pane>
      <splitpanes horizontal>
        <pane>
          <span>2</span>
        </pane>
        <pane>
          <span>3</span>
        </pane>
        <pane>
          <span>4</span>
        </pane>
      </splitpanes>
    </pane>
    <pane>
      <span>5</span>
    </pane>
  </splitpanes>
</template>

<script setup>
import { Splitpanes, Pane } from 'splitpanes'
import 'splitpanes/dist/splitpanes.css'
</script>
```

## 属性（Props）

### Splitpanes 组件

- `horizontal`: `Boolean`, 默认为 `false`。设置为 `true` 时，窗格将水平排列。
- `push-other-panes`: `Boolean`, 默认为 `true`。当一个窗格调整大小时，是否会推挤其他窗格。
- `rtl`: `Boolean`, 默认为 `false`。为从右到左的语言提供支持。
- `first-splitter`: `Boolean`, 默认为 `false`。在第一个窗格前添加一个分隔条。

### Pane 组件

- `size`: `Number`, 默认为 `null`。设置窗格的初始大小，单位是百分比。剩余空间将平均分配给未设置大小的窗格。
- `min-size`: `Number`, 默认为 `0`。窗格的最小尺寸。
- `max-size`: `Number`, 默认为 `100`。窗格的最大尺寸。

## 事件（Events）

`splitpanes` 提供了一系列事件来监听窗格的变化：

- `ready`: 在 `splitpanes` 初始化并且所有窗格都挂载后触发。
- `resize`: 在调整任何窗格大小时触发，参数为包含每个窗格大小的数组。
- `pane-click`: 点击窗格时触发，参数为被点击的窗格对象。
- `pane-maximize`: 当窗格最大化或恢复时触发。
- `splitter-click`: 当点击分隔条时触发。

### 事件使用示例

```vue
<template>
  <splitpanes @resize="onResize" style="height: 400px">
    <pane v-for="i in 3" :key="i">
      <span>{{ i }}</span>
    </pane>
  </splitpanes>
</template>

<script setup>
import { Splitpanes, Pane } from 'splitpanes'
import 'splitpanes/dist/splitpanes.css'

const onResize = (event) => {
  console.log(event.map(({ size }) => size))
}
</script>
```

## 总结

`splitpanes` 是一个现代化且功能丰富的 `vue-splitpane` 替代品。通过本文档的指引，你可以轻松地完成迁移，并利用其更多高级功能来构建灵活的页面布局。更多详细信息和示例，请参考[官方文档](https://antoniandre.github.io/splitpanes/)。 