---
source: vue-calendar-component, vue-sweet-calendar
target: Element Plus Calendar
link: https://element-plus.org/zh-CN/component/calendar.html
---

# 从 vue-calendar-component / vue-sweet-calendar 迁移到 Element Plus Calendar

`Element Plus` 是一个功能强大的 Vue 3 UI 库，其 `Calendar` (日历) 组件可以作为 `vue-calendar-component` 或 `vue-sweet-calendar` 等独立日历插件的优秀替代品。迁移到 `Element Plus Calendar` 意味着你将获得一个设计统一、功能丰富且持续维护的组件。

本指南将重点介绍如何使用 `Element Plus Calendar` 来实现与旧插件类似的功能，特别是如何在日历上展示事件。

## 重要提示：关于依赖

迁移到 `Element Plus Calendar` 需要你的项目中已经安装或计划引入 `Element Plus`。如果你尚未安装，请根据 [Element Plus 官方文档](https://element-plus.org/zh-CN/guide/installation.html) 进行安装。

```bash
npm install element-plus
```

## 主要区别

- **生态系统**: `vue-calendar-component` 和 `vue-sweet-calendar` 是独立的日历插件，而 `el-calendar` 是 Element Plus UI 套件的一部分。
- **事件展示**: 旧插件通常有专门的 `events` 属性来接收事件数组并自动渲染。`el-calendar` 则通过作用域插槽（scoped slot）提供了极高的灵活性，让你完全控制每个日期单元格的渲染内容。这意味着你需要自己编写逻辑来展示事件。
- **功能和定制化**: `el-calendar` 提供了更丰富的定制选项，例如自定义头部、日期范围等。

## 使用方法

### 1. 基本用法

`Element Plus Calendar` 的基本用法非常简单。你可以使用 `v-model` 来绑定当前选中的日期。

```vue
<template>
  <el-calendar v-model="value" />
</template>

<script lang="ts" setup>
import { ref } from 'vue'
const value = ref(new Date())
</script>
```

### 2. 展示事件（核心迁移部分）

这是与 `vue-sweet-calendar` 等插件最主要的区别。你需要使用 `el-calendar` 的默认作用域插槽来检查特定日期是否有事件，并进行渲染。

假设你有如下的事件列表：

```javascript
const events = ref([
  { date: '2023-11-15', title: '团队会议' },
  { date: '2023-11-20', title: '项目发布' },
  { date: '2023-11-20', title: '另一个会议' }
])
```

你可以这样改造你的 `el-calendar` 来展示它们：

```vue
<template>
  <el-calendar>
    <template #date-cell="{ data }">
      <p :class="data.isSelected ? 'is-selected' : ''">
        {{ data.day.split('-').slice(1).join('-') }}
        {{ data.isSelected ? '✔️' : '' }}
      </p>
      <div v-for="event in getEventsForDate(data.day)" :key="event.title">
        <el-tag size="small">{{ event.title }}</el-tag>
      </div>
    </template>
  </el-calendar>
</template>

<script setup>
import { ref } from 'vue'

const events = ref([
  { date: '2023-11-15', title: '团队会议' },
  { date: '2023-11-20', title: '项目发布' },
  { date: '2023-11-20', title: '另一个会议' }
])

// 辅助函数，用于获取特定日期的事件
const getEventsForDate = (date) => {
  return events.value.filter(event => event.date === date)
}
</script>

<style>
.is-selected {
  color: #1989fa;
}
.el-calendar-table .el-calendar-day {
  height: auto;
  min-height: 80px;
}
</style>
```

**迁移逻辑**:
1.  **获取日期**: 插槽暴露了 `data` 对象，其中 `data.day` 是 `YYYY-MM-DD` 格式的日期字符串。
2.  **匹配事件**: 创建一个方法（如 `getEventsForDate`），根据 `data.day` 从你的事件数组中筛选出匹配的事件。
3.  **渲染事件**: 遍历筛选出的事件，并使用你喜欢的任何方式（如 `el-tag`）来展示它们。

### 3. 自定义头部

`el-calendar` 也允许你自定义头部，以添加"回到今天"或月份切换等功能。

```vue
<template>
  <el-calendar ref="calendar">
    <template #header="{ date }">
      <span>{{ date }}</span>
      <el-button-group>
        <el-button size="small" @click="selectDate('prev-month')">上个月</el-button>
        <el-button size="small" @click="selectDate('today')">今天</el-button>
        <el-button size="small" @click="selectDate('next-month')">下个月</el-button>
      </el-button-group>
    </template>
    <!-- ... date-cell 插槽 ... -->
  </el-calendar>
</template>

<script setup>
import { ref } from 'vue'
const calendar = ref()
const selectDate = (val) => {
  calendar.value.selectDate(val)
}
</script>
```

## 总结

迁移到 `Element Plus Calendar` 需要你转变思路：从一个"配置驱动"的日历转变为一个"插槽驱动"的日历。虽然这需要你编写更多的自定义逻辑来展示事件，但它也提供了无与伦比的灵活性和定制能力，能够更好地融入你的应用设计中。

请务必仔细阅读 [Element Plus Calendar 官方文档](https://element-plus.org/zh-CN/component/calendar.html) 以了解所有可用的属性和插槽。 