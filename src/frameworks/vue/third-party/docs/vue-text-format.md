---
title: 迁移 vue-text-format
---

# 迁移指南: `vue-text-format`

本指南将引导你将 `vue-text-format` 从 Vue 2 项目迁移到 Vue 3 项目。

`vue-text-format` 是一个提供强大文本格式化功能的库，类似于 Excel 等电子表格应用程序中的功能。幸运的是，该库与 Vue 2 和 Vue 3 都兼容，使得迁移过程相对简单。主要的变化在于更新应用程序入口文件中插件的注册方式。

## 主要变化

1.  **NPM 包**: npm 包名保持不变：`vue-text-format`。你的 `package.json` 中关于包名的部分无需更改，但应确保你使用的是与 Vue 3 兼容的版本。
2.  **插件注册**: 插件的注册方法从 Vue 2 中的 `Vue.use()` 变为 Vue 3 中的 `app.use()`。
3.  **API 用法**: 指令 `v-format` 和全局函数 `$textFormat` 仍然可用。在组合式 API 中访问全局函数的方式略有改变。

---

## 分步迁移指南

### 1. 更新 `main.js` 中的插件注册

主要的变化在你的 `src/main.js` (或 `src/main.ts`) 文件中。你需要从 Vue 2 的插件注册语法切换到 Vue 3 的基于实例的方法。

#### Vue 2 示例 (`main.js`)

在你的 Vue 2 应用中，你会像这样注册插件：

```javascript
import Vue from 'vue';
import format from 'src/frameworks/vue/third-party/docs/vue-text-format';
import App from './App.vue';

Vue.use(format);

new Vue({
	render: h => h(App),
}).$mount('#app');
```

#### Vue 3 示例 (`main.js`)

在 Vue 3 中，你将使用 `createApp` 实例来注册插件：

```javascript
import { createApp } from 'vue';
import format from 'src/frameworks/vue/third-party/docs/vue-text-format';
import App from './App.vue';

const app = createApp(App);

app.use(format);
app.mount('#app');
```

**注意:** `vue-text-format` 的官方文档（在 `vue-format` 仓库名下）将其 Vue 2 和 Vue 3 的注册示例搞混了。这里提供的示例是正确的。

### 2. 使用 `v-format` 指令

`v-format` 指令在 Vue 2 和 Vue 3 中的工作方式完全相同。你的模板中无需任何更改。

```vue
<template>
  <div>
    <!-- 将数字格式化为百分比 -->
    <div v-format="'0.##%'">0.12345</div>
    <!-- 结果: 12.35% -->

    <!-- 使用千位分隔符格式化数字 -->
    <div v-format="'#,##0.00'">1234567.89</div>
    <!-- 结果: 1,234,567.89 -->
  </div>
</template>
```

### 3. 使用 `$textFormat` 函数

该库还提供了一个全局函数 `$textFormat`。

-   在**选项式 API** (Vue 2 和 Vue 3) 中，你可以继续通过 `this.$textFormat` 访问它。

-   在**组合式 API** (Vue 3) 中，你可以从组件实例中访问它。

#### 组合式 API 示例 (`<script setup>`)

```vue
<script setup>
import { getCurrentInstance, onMounted } from 'vue';

const { proxy } = getCurrentInstance();

onMounted(() => {
  const formattedValue = proxy.$textFormat(0.987, '0.0%');
  console.log(formattedValue); // 输出: 98.7%
});
</script>
```

## 总结

`vue-text-format` 的迁移工作量很小。其核心功能和 API 在 Vue 2 和 Vue 3 之间保持一致。唯一需要更改的是更新应用程序入口文件中的插件注册语法。之后，你现有的 `v-format` 指令和函数调用应该可以正常工作。 
