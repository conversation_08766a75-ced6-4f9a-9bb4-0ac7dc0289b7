---
title: '从 vue-uuid 迁移到 vue3-uuid'
source: 'vue-uuid'
target: 'vue3-uuid'
---

`vue3-uuid` 是一个为 Vue 3 优化的 UUID 生成库，它是 `vue-uuid` 的一个分支，解决了在 Vue 3 项目中的兼容性问题。迁移过程非常简单，主要集中在依赖更新和 API 使用方式的现代化。

## 1. 依赖更新

首先，更新你的 `package.json` 文件，将 `vue-uuid` 替换为 `vue3-uuid`。

```bash
npm uninstall vue-uuid
npm install vue3-uuid
```

## 2. 全局注册 (main.js)

`vue-uuid` 针对 Vue 3 的早期版本提供了一个 `withUUID` 的 HOC（高阶组件）用法，而 `vue3-uuid` 采用了更标准的插件注册方式。

### Vue 2 / 旧版 Vue 3 用法

你可能在 `main.js` 中有类似这样的代码：

```javascript
// vue-uuid with Vue 2
import UUID from 'src/frameworks/vue/third-party/docs/vue-uuid';

Vue.use(UUID);

// or vue-uuid with early Vue 3
import withUUID from "src/frameworks/vue/third-party/docs/vue-uuid";

const app = withUUID(createApp(App));
```

### Vue 3 (新版) 用法

使用 `vue3-uuid`，注册方式变得更简洁：

```javascript
import { createApp } from 'vue';
import App from './App.vue';
import UUID from 'vue3-uuid';

const app = createApp(App);
app.use(UUID);
app.mount('#app');
```
这个插件会将 `uuid` 对象注入到全局，可以通过 `this.$uuid` 访问。然而，在 Vue 3 中，更推荐直接导入使用。

## 3. 组件内用法

在 Vue 3 的组合式 API (Composition API) 中，最佳实践是直接从库中导入所需的功能，而不是依赖全局注入。这有助于更好地进行摇树优化（Tree-shaking）。

### 旧用法 (Options API)

在 Vue 2 的选项式 API 或 Vue 3 中沿用旧习惯，你可能会这样使用：

```javascript
export default {
  mounted() {
    const newId = this.$uuid.v4();
    console.log(newId);
  }
}
```

### 推荐用法 (Composition API)

我们强烈建议在 `setup` 脚本中直接导入 `uuid` 对象。

```html
<template>
  <div>
    <p>生成的 UUID: {{ generatedUuid }}</p>
    <button @click="generateNewUuid">生成新的 UUID</button>
  </div>
</template>

<script setup>
import { ref } from 'vue';
// 直接从 'vue3-uuid' 导入
import { uuid } from 'vue3-uuid';

const generatedUuid = ref(uuid.v4());

function generateNewUuid() {
  generatedUuid.value = uuid.v4();
}
</script>
```

**核心优势**:
- **无需全局注册**: 如果你在所有组件中都采用直接导入的方式，你甚至可以省略在 `main.js` 中的 `app.use(UUID)`。
- **类型安全和摇树优化**: 直接导入使得静态分析工具更容易跟踪依赖，从而移除未使用的代码。
- **代码清晰**: 依赖关系在组件内部一目了然。

### UUID 版本

`vue3-uuid` 支持生成 v1, v3, v4, v5 版本的 UUID，用法与 `uuid` 库标准一致：

- `uuid.v1()`: 基于时间戳的 UUID。
- `uuid.v4()`: 随机生成的 UUID（最常用）。
- `uuid.v3(name, namespace)`: 基于命名空间和名字 (MD5) 的 UUID。
- `uuid.v5(name, namespace)`: 基于命名空间和名字 (SHA-1) 的 UUID。

```javascript
import { uuid } from 'vue3-uuid';

const NAMESPACE = '...'; // 一个有效的 UUID 字符串

const id_v1 = uuid.v1();
const id_v4 = uuid.v4();
const id_v5 = uuid.v5('hello.world', NAMESPACE);
```
