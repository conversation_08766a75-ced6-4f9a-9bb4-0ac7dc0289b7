---
source: "@riophae/vue-treeselect"
target: "el-tree-select"
link: "https://element-plus.org/zh-CN/component/tree-select.html"
---

# 从 @riophae/vue-treeselect 迁移到 Element Plus Tree Select

`Element Plus` 的 `Tree Select` (树形选择) 组件是一个功能强大且与 Element Plus 生态系统无缝集成的组件，可以作为 `@riophae/vue-treeselect` 的现代化替代品。迁移到 `el-tree-select` 可以帮助你统一项目中的 UI 风格并减少对单个独立库的依赖。

## 重要提示：关于依赖

迁移到 `el-tree-select` 需要你的项目中已经安装或计划引入 `Element Plus`。如果你尚未安装，请根据 [Element Plus 官方文档](https://element-plus.org/zh-CN/guide/installation.html) 进行安装和配置。

```bash
npm install element-plus
# 卸载旧的 treeselect
npm uninstall @riophae/vue-treeselect
```

## 主要区别与迁移步骤

### 1. 组件和样式

- **组件替换**: 将模板中的 `<treeselect>` 组件替换为 `<el-tree-select>`。
- **移除旧样式**: 删除 `@riophae/vue-treeselect/dist/vue-treeselect.css` 的导入。Element Plus 的样式会统一处理。

### 2. 数据格式 (最关键)

两个组件接收的数据结构默认键名不同，这是迁移中最需要注意的地方。

- `@riophae/vue-treeselect` 默认使用 `id`, `label`, `children`。
- `el-tree-select` 默认使用 `value`, `label`, `children`。

**迁移方案：使用 `props` 属性**

`el-tree-select` 提供了一个强大的 `props` 属性，允许你映射自定义的键名，而无需修改原始数据。这是最推荐的迁移方式。

**`@riophae/vue-treeselect` 示例:**
```vue
<template>
  <treeselect
    :options="options"
    v-model="value"
  />
</template>

<script>
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'

export default {
  components: { Treeselect },
  data() {
    return {
      value: null,
      options: [{
        id: 'a',
        label: 'a',
        children: [ /* ... */ ],
      }],
    }
  },
}
</script>
```

**迁移到 `el-tree-select`:**
```vue
<template>
  <el-tree-select
    v-model="value"
    :data="options"
    :props="treeProps"
    style="width: 240px"
  />
</template>

<script setup>
import { ref } from 'vue'

const value = ref(null)
const options = ref([{
  id: 'a',
  label: 'a',
  children: [ /* ... */ ],
}])

// 定义 props 映射
const treeProps = {
  value: 'id', // 将 'id' 映射到 'value'
  label: 'label',
  children: 'children',
}
</script>
```

### 3. 属性（Props）映射

许多常用的属性可以找到直接或间接的对应。

| `@riophae/vue-treeselect` | `el-tree-select`                 | 说明                                                                 |
| ------------------------- | -------------------------------- | -------------------------------------------------------------------- |
| `v-model="value"`         | `v-model="value"`                | 绑定值，保持不变。                                                   |
| `:options="options"`      | `:data="options"`                | 传递树形数据的属性名从 `options` 变为 `data`。                       |
| `:multiple="true"`        | `:multiple="true"`               | 多选模式，保持不变。                                                 |
| `:searchable="true"`      | `:filterable="true"`             | 搜索功能，属性名从 `searchable` 变为 `filterable`。                  |
| `:clearable="true"`       | `:clearable="true"`              | 清除按钮，保持不变。                                                 |
| `:disabled="true"`        | `:disabled="true"`               | 禁用状态，保持不变。                                                 |
| `placeholder`             | `placeholder`                    | 占位符文本，保持不变。                                               |
| `disableBranchNodes`      | `check-strictly` / `props.disabled` | `el-tree-select` 中，可使用 `check-strictly` 来实现父子节点不关联。或通过 `props` 属性定义 `disabled` 键来禁用特定分支。 |
| `flat`                    | `check-strictly`                 | 在 `el-tree-select` 中，`check-strictly` 提供了类似的功能，使得父子选择不关联。 |

### 4. 异步加载

`vue-treeselect` 使用 `load-options` 属性进行异步加载。`el-tree-select` 则使用 `lazy` 和 `load` 属性。

**`el-tree-select` 异步加载示例:**

```vue
<template>
  <el-tree-select
    v-model="value"
    lazy
    :load="load"
    :props="treeProps"
  />
</template>

<script setup>
const treeProps = {
  label: 'label',
  children: 'children',
  isLeaf: 'isLeaf',
}

const load = (node, resolve) => {
  if (node.isLeaf) return resolve([])

  // 模拟异步请求
  setTimeout(() => {
    resolve([
      { value: 'leaf1', label: 'leaf1', isLeaf: true },
      { value: 'leaf2', label: 'leaf2', isLeaf: true },
    ])
  }, 400)
}
</script>
```

## 总结

从 `@riophae/vue-treeselect` 迁移到 `Element Plus Tree Select` 的核心是处理好数据结构的差异，通过 `el-tree-select` 的 `props` 属性可以轻松解决这个问题。同时，需要注意一些功能属性名称的变化。迁移后，你将拥有一个与 Element Plus 生态系统完美融合的、功能强大的树形选择组件。

建议仔细阅读 [Element Plus Tree Select 官方文档](https://element-plus.org/zh-CN/component/tree-select.html) 以探索其更多高级功能。 
