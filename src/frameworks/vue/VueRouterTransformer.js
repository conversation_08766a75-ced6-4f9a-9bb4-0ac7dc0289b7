const gogocode = require('gogocode');
const chalk = require('chalk');

/**
 * Vue Router 3 到 Vue Router 4 迁移转换器
 * 主要处理 router.addRoutes() 到 router.addRoute() 的转换
 */
class VueRouterTransformer {
  constructor(options = {}) {
    this.options = Object.assign({
      verbose: false
    }, options);
    
    this.stats = {
      success: 0,
      failed: 0,
      transformations: []
    };
  }

  /**
   * 转换代码中的 Vue Router API
   * @param {string} code - 源代码
   * @param {string} filePath - 文件路径（用于调试）
   * @returns {string} 转换后的代码
   */
  async transform(code, filePath = '') {
    try {
      let transformedCode = code;
      let hasChanges = false;

      // 使用 gogocode 进行 AST 转换
      const astResult = await this.transformWithAST(transformedCode, filePath);
      if (astResult.success && astResult.code !== transformedCode) {
        transformedCode = astResult.code;
        hasChanges = true;
        this.stats.transformations.push({
          type: 'ast-transform',
          file: filePath,
          changes: astResult.changes
        });
      }

      // 如果 AST 转换失败，尝试正则表达式转换作为后备
      if (!astResult.success) {
        const regexResult = this.transformWithRegex(transformedCode, filePath);
        if (regexResult.code !== transformedCode) {
          transformedCode = regexResult.code;
          hasChanges = true;
          this.stats.transformations.push({
            type: 'regex-transform',
            file: filePath,
            changes: regexResult.changes
          });
        }
      }

      if (hasChanges) {
        this.stats.success++;
        if (this.options.verbose) {
          console.log(chalk.green(`✅ Vue Router 转换成功: ${filePath}`));
        }
      }

      return transformedCode;
    } catch (error) {
      this.stats.failed++;
      if (this.options.verbose) {
        console.warn(chalk.yellow(`⚠️ Vue Router 转换失败: ${filePath} - ${error.message}`));
      }
      return code;
    }
  }

  /**
   * 使用 gogocode AST 进行转换
   * @param {string} code - 源代码
   * @param {string} filePath - 文件路径
   * @returns {Object} 转换结果
   */
  async transformWithAST(code, filePath) {
    try {
      const ast = gogocode(code);
      let hasChanges = false;
      const changes = [];

      // 查找 router.addRoutes() 调用
      const addRoutesNodes = ast.find('$_$.addRoutes($$$)');
      
      if (addRoutesNodes.length > 0) {
        addRoutesNodes.each((node) => {
          try {
            // 获取调用的对象（通常是 router）
            const callee = node.attr('callee');
            if (callee && callee.object && callee.property) {
              const routerName = callee.object.name || 'router';
              const args = node.attr('arguments');
              
              if (args && args.length > 0) {
                const routesArg = args[0];
                const routesArgCode = gogocode(routesArg).generate();
                
                // 生成新的 forEach 代码
                const newCode = `${routesArgCode}.forEach(route => {
  ${routerName}.addRoute(route)
})`;
                
                // 替换整个调用表达式
                node.replaceBy(newCode);
                hasChanges = true;
                changes.push({
                  type: 'addRoutes-to-forEach',
                  original: node.generate(),
                  transformed: newCode
                });
                
                if (this.options.verbose) {
                  console.log(chalk.gray(`  转换 ${routerName}.addRoutes() 为 forEach 循环`));
                }
              }
            }
          } catch (nodeError) {
            if (this.options.verbose) {
              console.warn(chalk.yellow(`⚠️ 转换单个节点失败: ${nodeError.message}`));
            }
          }
        });
      }

      return {
        success: true,
        code: hasChanges ? ast.generate() : code,
        changes: changes
      };
    } catch (error) {
      if (this.options.verbose) {
        console.warn(chalk.yellow(`⚠️ AST 转换失败: ${error.message}`));
      }
      return {
        success: false,
        error: error.message,
        changes: []
      };
    }
  }

  /**
   * 使用正则表达式进行转换（后备方案）
   * @param {string} code - 源代码
   * @param {string} filePath - 文件路径
   * @returns {Object} 转换结果
   */
  transformWithRegex(code, filePath) {
    let transformedCode = code;
    const changes = [];

    // 匹配 router.addRoutes(routes) 模式
    const addRoutesPattern = /(\w+)\.addRoutes\s*\(\s*([^)]+)\s*\)/g;
    
    transformedCode = transformedCode.replace(addRoutesPattern, (match, routerName, routesArg) => {
      const newCode = `${routesArg}.forEach(route => {
  ${routerName}.addRoute(route)
})`;
      
      changes.push({
        type: 'addRoutes-to-forEach',
        original: match,
        transformed: newCode
      });
      
      if (this.options.verbose) {
        console.log(chalk.gray(`  正则转换 ${routerName}.addRoutes() 为 forEach 循环`));
      }
      
      return newCode;
    });

    return {
      code: transformedCode,
      changes: changes
    };
  }

  /**
   * 检查代码中是否包含需要转换的 Vue Router API
   * @param {string} code - 源代码
   * @returns {boolean} 是否包含需要转换的内容
   */
  hasVueRouterContent(code) {
    // 检查是否包含 addRoutes 调用
    return /\.addRoutes\s*\(/.test(code);
  }

  /**
   * 获取转换统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    return {
      ...this.stats,
      total: this.stats.success + this.stats.failed
    };
  }

  /**
   * 重置统计信息
   */
  resetStats() {
    this.stats = {
      success: 0,
      failed: 0,
      transformations: []
    };
  }
}

module.exports = VueRouterTransformer;
