const chalk = require('chalk')
const gogocode = require('gogocode')

class GogocodeTransform {
	constructor(options = {}) {
		this.options = {
			verbose: false,
			maxRetries: 3,
			fallbackToOriginal: true,
			...options
		}
		this.errorLog = []
	}

	safeVueTransform(vueTransform, fileInfo, api, options) {
		if (!fileInfo || !fileInfo.source) {
			throw new Error('文件信息无效')
		}

		if (!api || !api.gogocode) {
			throw new Error('API 对象无效')
		}

		// 执行转换
		let result = vueTransform(fileInfo, api, options)

		// 验证结果
		if (typeof result !== 'string') {
			throw new Error(`Vue 转换返回非字符串: ${typeof result}`)
		}

		// 语法检查
		if (fileInfo.path.endsWith('.vue')) {
			this.validateVueFileStructure(result, fileInfo.source)
		} else {
			this.validateJavaScriptSyntax(result)
		}

		return result
	}

	validateVueFileStructure(transformed, original) {
		const originalHasTemplate = original.includes('<template>')
		const originalHasScript = original.includes('<script>')
		const originalHasStyle = original.includes('<style>')

		const transformedHasTemplate = transformed.includes('<template>')
		const transformedHasScript = transformed.includes('<script>')
		const transformedHasStyle = transformed.includes('<style>')

		if (originalHasTemplate && !transformedHasTemplate) {
			throw new Error('转换后丢失 template 部分')
		}

		if (originalHasScript && !transformedHasScript) {
			throw new Error('转换后丢失 script 部分')
		}

		// style 部分丢失不是致命错误，只警告
		if (originalHasStyle && !transformedHasStyle && this.options.verbose) {
			console.warn(chalk.yellow('⚠️  转换后丢失 style 部分'))
		}
	}

	validateJavaScriptSyntax(code) {
		try {
			gogocode(code)
		} catch (syntaxError) {
			throw new Error(`语法错误: ${syntaxError.message}`)
		}
	}

	logError(filePath, errorType, errorMessage, result) {
		this.errorLog.push({
			filePath,
			errorType,
			errorMessage,
			result,
			timestamp: new Date().toISOString()
		})
	}

	/**
	 * 获取错误统计
	 */
	getErrorStats() {
		const byType = {}
		this.errorLog.forEach(error => {
			byType[error.errorType] = (byType[error.errorType] || 0) + 1
		})

		return {
			total: this.errorLog.length,
			byType,
			errors: this.errorLog
		}
	}
}

module.exports = GogocodeTransform
