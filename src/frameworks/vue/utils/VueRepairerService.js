const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const { AIService } = require('../../../ai/AiService');

/**
 * @deprecated
 */
class VueRepairerService extends AIService {
  constructor(options = {}) {
    super(options);

    if (this.enabled) {
      console.log(chalk.green(`✅ AI 修复功能已启用 (${this.llmConfig.providerName})`));
    }
  }

  async repairSingleFile(failedFile, projectPath) {
    const filePath = path.isAbsolute(failedFile.absolutePath)
      ? failedFile.absolutePath
      : path.join(projectPath, failedFile.file);

    console.log(chalk.gray(`🔧 修复: ${failedFile.file}...`));

    try {
      const originalContent = await fs.readFile(filePath, 'utf8');
      const prompt = this.generateRepairPrompt(originalContent, failedFile);

      const repairedContent = await this.callAI(prompt);

      if (this.validateRepairedContent(repairedContent, originalContent)) {
        // await this.backupFile(filePath);
        await fs.writeFile(filePath, repairedContent, 'utf8');

        console.log(chalk.green(`✅ 修复成功: ${failedFile.file}`));
        return {
          file: failedFile.file,
          success: true,
          originalSize: originalContent.length,
          repairedSize: repairedContent.length
        };
      } else {
        console.log(chalk.yellow(`⚠️  修复结果验证失败: ${failedFile.file}`));
        return {
          file: failedFile.file,
          success: false,
          error: 'Validation failed'
        };
      }
    } catch (error) {
      console.log(chalk.red(`❌ 修复失败: ${failedFile.file}`));
      return {
        file: failedFile.file,
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 生成修复提示
   */
  generateRepairPrompt(originalContent, failedFile) {
    const fileExtension = path.extname(failedFile.file);
    const errorMessage = failedFile.error;

    // 检查是否是 Sass 文件
    if (fileExtension === '.scss' || fileExtension === '.sass') {
      return this.generateSassRepairPrompt(originalContent, failedFile);
    }

    let prompt = `将如下的 Vue 2 + Element UI 组件代码，迁移到 Vue 3 + Element Plus 组件代码

**迁移要求**:
1. 状态管理：如果原来的代码使用 Vuex 状态管理，请转换为 Vuex 4.x 语法，如果没有用到状态管理就不要添加；
2. 功能复刻：尽可能少的变更语法，以避免改变原有逻辑;

**关键迁移点 (基于常见错误模式)**:
${this.getKeyMigrationPoints(errorMessage, fileExtension, failedFile.file)}

**原始错误**: ${errorMessage}

**原始代码**:
\`\`\`${fileExtension.slice(1)}
${originalContent}
\`\`\`

请使用 ${fileExtension.slice(1)} 代码块返回修复后的完整代码，方便我直接使用。`;

    // 根据文件类型添加特定指导
    if (fileExtension === '.vue') {
      prompt += `

**Vue 文件特定要求**:
- 更新 <template> 中的 Element UI 组件为 Element Plus
- 更新事件处理和 refs 访问方式
- 确保 props 和 emits 正确定义
- 如果使用函数式组件，移除 functional: true，改为普通组件
- DOM操作需要在 nextTick 中进行，确保元素已渲染`;
    } else if (fileExtension === '.js' || fileExtension === '.ts') {
      prompt += `

**JavaScript 文件特定要求**:
- 将 Vue.extend 替换为 defineComponent
- 将 new Vue() 替换为 createApp()
- 更新 Vue 插件注册方式
- 修复导入语句`;
    }

    return prompt;
  }

  /**
   * 根据错误信息、文件路径和文件类型生成关键迁移点提示
   */
  getKeyMigrationPoints(errorMessage, fileExtension, filePath) {
    const points = [];

    // 路由相关错误
    if (errorMessage.includes('addRoutes is not a function')) {
      points.push('- 路由API变更：将 router.addRoutes(routes) 改为 routes.forEach(route => router.addRoute(route))');
    }

    if (errorMessage.includes('Route paths should start with')) {
      points.push('- 路由路径格式：确保所有路由路径以 "/" 开头，如 path: "/dashboard"');
    }

    // 异步组件错误
    if (errorMessage.includes('loader is not a function') || errorMessage.includes('defineAsyncComponent')) {
      points.push('- 异步组件：使用 defineAsyncComponent(() => import("./Component")) 替代直接的 () => import()');
    }

    // 组件方法访问错误
    if (errorMessage.includes('is not a function') && errorMessage.includes('$refs')) {
      points.push('- 组件方法暴露：在子组件中使用 defineExpose({methodName}) 或 expose: ["methodName"] 暴露方法给父组件');
    }

    // DOM操作错误
    if (errorMessage.includes('addEventListener') || errorMessage.includes('Cannot read properties of undefined')) {
      points.push('- DOM引用：使用 nextTick(() => {}) 确保DOM已渲染，并检查 this.$refs.element 是否存在');
    }

    // 过滤器错误
    if (errorMessage.includes('Filter') || errorMessage.includes('toThousandFilter')) {
      points.push('- 全局过滤器：Vue 3 移除了过滤器，改为全局属性或方法调用，如 {{ $filters.toThousandFilter(value) }}');
    }

    // Element UI 相关错误
    if (errorMessage.includes('element-ui') || errorMessage.includes('Element')) {
      points.push('- Element Plus迁移：将 import { Message } from "element-ui" 改为 import { ElMessage } from "element-plus"');
      points.push('- 图标更新：将 <i class="el-icon-edit"></i> 改为 <el-icon><Edit /></el-icon>');
    }

    // Vuex 相关错误
    if (errorMessage.includes('Vuex') || errorMessage.includes('store')) {
      points.push('- Vuex 4.x：使用 createStore() 替代 new Vuex.Store()，移除 Vue.use(Vuex)');
    }

    // 全局API错误
    if (errorMessage.includes('Vue.') || errorMessage.includes('config')) {
      points.push('- 全局API：将 Vue.config.xxx 改为 app.config.xxx，Vue.component() 改为 app.component()');
    }

    // ECharts 相关错误
    if (errorMessage.includes('echarts') || errorMessage.includes('ECharts')) {
      points.push('- ECharts升级：升级到 ECharts 5.x，使用 import * as echarts from "echarts"');
    }

    // process 对象错误
    if (errorMessage.includes('process is not defined')) {
      points.push('- Node.js兼容性：在 vue.config.js 中配置 webpack ProvidePlugin 提供 process polyfill');
    }

    // 根据文件路径和类型添加特定指导
    this.addFileSpecificGuidance(points, filePath, fileExtension);

    return points.length > 0 ? points.join('\n') : '- 请参考Vue 3迁移指南进行相应的语法更新';
  }

  /**
   * 根据文件路径添加特定的修复指导
   */
  addFileSpecificGuidance(points, filePath, fileExtension) {
    // 主入口文件
    if (filePath.includes('main.js') || filePath.includes('main.ts')) {
      points.push('- 入口文件：使用 createApp(App).use(router).use(store).mount("#app")');
      points.push('- 全局组件注册：使用 app.component() 替代 Vue.component()');
      points.push('- 全局属性：使用 app.config.globalProperties 替代 Vue.prototype');
    }

    // 路由文件
    if (filePath.includes('router') && (fileExtension === '.js' || fileExtension === '.ts')) {
      points.push('- 路由配置：const router = createRouter({\n  history: createWebHashHistory(),\n  scrollBehavior: () => ({ top: 0 }),\n  routes: constantRoutes\n})');
      points.push('- 路由模式：createWebHistory() 对应 history 模式，createWebHashHistory() 对应 hash 模式');
      points.push('- 通配符路由：将 path: "*" 改为 path: "/:pathMatch(.*)*"');
    }

    // 权限文件
    if (filePath.includes('permission.js') || filePath.includes('permission.ts')) {
      points.push('- 动态路由：将 router.addRoutes() 改为 routes.forEach(route => router.addRoute(route))');
      points.push('- 路由守卫：确保 next() 调用正确，避免无限循环');
    }

    // 状态管理文件
    if (filePath.includes('store') && (fileExtension === '.js' || fileExtension === '.ts')) {
      points.push('- Vuex 4.x：使用 createStore() 替代 new Vuex.Store()');
      points.push('- 模块导入：确保所有 store 模块正确导入和注册');
    }

    // 工具文件
    if (filePath.includes('utils') || filePath.includes('helpers')) {
      points.push('- 工具函数：避免直接使用 Vue 实例，通过参数传递或全局属性访问');
      points.push('- 消息提示：使用 ElMessage 替代 this.$message');
    }

    // 组件文件
    if (fileExtension === '.vue') {
      points.push('- Vue组件：确保 <template> 可以有多个根节点，更新事件监听器语法');
      points.push('- 组件通信：使用 defineEmits 声明事件，使用 defineExpose 暴露方法');

      // 特定组件类型
      if (filePath.includes('Layout') || filePath.includes('layout')) {
        points.push('- 布局组件：检查 <router-view> 的使用，确保插槽语法正确');
      }

      if (filePath.includes('Table') || filePath.includes('table')) {
        points.push('- 表格组件：Element Plus 表格事件名称可能有变化，检查 @selection-change 等事件');
      }

      if (filePath.includes('Form') || filePath.includes('form')) {
        points.push('- 表单组件：检查表单验证规则和 ref 引用方式');
      }
    }

    // 图标相关文件
    if (filePath.includes('icons') || filePath.includes('svg')) {
      points.push('- SVG图标：确保 svg-sprite-loader 配置正确，检查图标组件注册');
      points.push('- 图标组件：使用 app.component() 全局注册 SvgIcon 组件');
    }

    // 样式文件特殊处理
    if (filePath.includes('element-variables') || filePath.includes('theme')) {
      points.push('- 主题变量：Element Plus 变量名称可能有变化，检查 CSS 变量定义');
    }
  }

  /**
   * 生成 Sass 文件修复提示
   */
  generateSassRepairPrompt(originalContent, failedFile) {
    const fileExtension = path.extname(failedFile.file);
    const errorMessage = failedFile.error;

    let prompt = `请修复以下 Sass/SCSS 文件，将其从 Element UI 迁移到 Element Plus，并解决导入路径问题。

**修复要求**:
1. 将 Element UI 相关的导入路径更新为 Element Plus
2. 修复无法找到的 Sass 文件路径
3. 将 @import 语法迁移为 @use 语法（如果可能）
4. 保持所有变量定义和样式规则不变
5. 确保路径解析正确
6. 修复 SCSS 变量未定义错误

**常见路径映射**:
- \`~element-ui/packages/theme-chalk/src/index\` → \`element-plus/theme-chalk/src/index\`
- \`~element-ui/lib/theme-chalk/fonts\` → \`element-plus/lib/theme-chalk/fonts\`
- \`~element-ui/\` → \`element-plus/\`
- \`element-ui/lib/theme-chalk/index.css\` → \`element-plus/dist/index.css\`

**SCSS 变量问题解决**:
- 使用 @use 语法时，在文件顶部添加：@use 'variables' as *;
- 确保变量文件路径正确，如：@use '@/styles/variables' as *;
- 将 ::v-deep 改为 :deep() 语法

**原始错误**: ${errorMessage}

**原始代码**:
\`\`\`${fileExtension.slice(1)}
${originalContent}
\`\`\`

**修复指导**:`;

    // 根据错误类型提供特定指导
    if (errorMessage.includes('Could not find Sass file')) {
      prompt += `
- 这是一个路径解析错误，需要更新导入路径
- 检查 node_modules 中是否存在对应的文件
- 将 element-ui 路径替换为 element-plus 路径`;
    }

    if (errorMessage.includes('element-ui')) {
      prompt += `
- 将所有 element-ui 相关路径替换为 element-plus
- 注意 Element Plus 的目录结构可能与 Element UI 不同`;
    }

    prompt += `

请返回修复后的完整 ${fileExtension.slice(1)} 代码，确保：
1. 所有导入路径都是有效的
2. 保持原有的变量定义和样式
3. 使用正确的 Element Plus 路径`;

    return prompt;
  }

  printRepairStats() {
    console.log('\n' + chalk.bold('🤖 AI 修复统计:'));
    console.log(`尝试修复: ${this.stats.attempted} 个文件`);
    console.log(chalk.green(`✅ 成功: ${this.stats.success} 个`));
    console.log(chalk.red(`❌ 失败: ${this.stats.failed} 个`));

    if (this.stats.attempted > 0) {
      const successRate = ((this.stats.success / this.stats.attempted) * 100).toFixed(1);
      console.log(chalk.bold(`成功率: ${successRate}%`));
    }
  }

}

module.exports = VueRepairerService;
