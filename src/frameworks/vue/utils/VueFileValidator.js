const chalk = require('chalk')

/**
 * VueFileValidator - Vue 文件内容验证器
 *
 * 负责验证修复后的 Vue 文件内容是否符合规范
 */
class VueFileValidator {
	constructor (options = {}) {
		this.options = {
			verbose: false,
			...options
		}
	}

	validateContent (fixedContent, originalContent, filePath = '') {
		if (!fixedContent) {
			return {
				isValid: false,
				error: '修复内容为空',
				content: null
			}
		}

		let processedContent = fixedContent

		if (this.containsXmlTags(fixedContent)) {
			if (this.options.verbose) {
				console.log(chalk.gray('       🔧 检测到 XML 标签，尝试提取内容...'))
			}

			const extractedContent = this.extractFromXmlTags(fixedContent)
			if (extractedContent) {
				processedContent = extractedContent
				if (this.options.verbose) {
					console.log(chalk.gray(`       ✅ 成功从 XML 标签中提取内容，长度: ${extractedContent.length} 字符`))
				}
			} else {
				return {
					isValid: false,
					error: '无法从 XML 标签中提取有效内容',
					content: null
				}
			}
		}

		const normalizedFixed = this.normalizeContent(processedContent)
		const normalizedOriginal = this.normalizeContent(originalContent)

		if (this.options.verbose) {
			console.log(chalk.gray(`       🔍 内容验证: 原文件${normalizedOriginal.length}字符, 修复后${normalizedFixed.length}字符`))
		}

		const isVueFile = filePath.endsWith('.vue')
		if (isVueFile) {
			const vueValidation = this.validateVueFileContent(normalizedFixed)
			if (!vueValidation.isValid) {
				return vueValidation
			}
		}

		if (normalizedFixed === normalizedOriginal) {
			return this.handleIdenticalContent(processedContent, originalContent)
		}

		if (normalizedFixed.length < 10) {
			return {
				isValid: false,
				error: '修复后的内容过短，可能无效',
				content: null
			}
		}

		return {
			isValid: true,
			error: null,
			content: processedContent
		}
	}


	validateVueFileContent (content) {
		const trimmedContent = content.trim()

		const validVueStarts = ['<template', '<script', '<style']
		const startsWithValidTag = validVueStarts.some(tag =>
			trimmedContent.toLowerCase().startsWith(tag.toLowerCase())
		)

		if (!startsWithValidTag) {
			return {
				isValid: false,
				error: 'Vue 文件必须以 <template>、<script> 或 <style> 标签开头',
				content: null
			}
		}

		const hasTemplate = /<template[\s\S]*?<\/template>/i.test(content)
		const hasScript = /<script[\s\S]*?<\/script>/i.test(content)

		if (!hasTemplate && !hasScript) {
			return {
				isValid: false,
				error: 'Vue 文件必须包含 <template> 或 <script> 标签',
				content: null
			}
		}

		return {
			isValid: true,
			error: null,
			content: content
		}
	}

	handleIdenticalContent (fixedContent, originalContent) {
		const noFixIndicators = [
			'无需修复',
			'no fix needed',
			'already correct',
			'已经正确',
			'file is correct'
		]

		const hasNoFixIndicator = noFixIndicators.some(indicator =>
			fixedContent.toLowerCase().includes(indicator.toLowerCase())
		)

		if (hasNoFixIndicator) {
			if (this.options.verbose) {
				console.log(chalk.gray('       ℹ️  AI 判断文件无需修复'))
			}
			return {
				isValid: true,
				error: null,
				content: originalContent
			}
		} else {
			if (this.options.verbose) {
				console.log(chalk.gray('       ⚠️  AI 返回的内容与原文件相同，可能修复失败'))
			}
			return {
				isValid: false,
				error: 'AI 返回的内容与原文件相同，可能修复失败',
				content: null
			}
		}
	}

	normalizeContent (content) {
		return content
			.replace(/\r\n/g, '\n')
			.replace(/\s+$/gm, '')
			.trim()
	}

	containsXmlTags (content) {
		const xmlTags = ['<fix_result>', '<fixed_content>', '<changes_made>']
		return xmlTags.some(tag => content.includes(tag))
	}

	extractFromXmlTags (content) {
		const fixedContentMatch = content.match(/<fixed_content>([\s\S]*?)<\/fixed_content>/)
		if (fixedContentMatch) {
			return fixedContentMatch[1].trim()
		}

		const contentMatch = content.match(/<content>([\s\S]*?)<\/content>/)
		if (contentMatch) {
			return contentMatch[1].trim()
		}

		return null
	}
}

module.exports = VueFileValidator
