const gogocode = require('gogocode');
const chalk = require('chalk');

class RenderFunctionTransformer {
  constructor(options = {}) {
    this.options = {
      verbose: false,
      ...options
    };
    this.stats = {
      total: 0,
      astSuccess: 0,
      failed: 0,
      skipped: 0
    };
  }

  /**
   * 转换代码中的 renderContent 方法和功能性组件
   * @param {string} code - 源代码
   * @param {string} filePath - 文件路径（用于调试）
   * @returns {string} 转换后的代码
   */
  async transform(code, filePath = '') {
    try {
      // 检查是否是 export default function render 格式
      if (this.isExportDefaultRenderFunction(code)) {
        if (this.options.verbose) {
          console.log(chalk.gray(`🔄 检测到 export default function render，开始转换: ${filePath}`));
        }

        const exportRenderResult = await this.transformExportDefaultRenderFunction(code, filePath);
        if (exportRenderResult.success) {
          this.stats.astSuccess++;
          if (this.options.verbose) {
            console.log(chalk.green(`✅ export default function render 转换成功: ${filePath}`));
          }
          return exportRenderResult.code;
        }
      }

      // 检查是否是功能性组件
      if (this.isFunctionalComponent(code)) {
        if (this.options.verbose) {
          console.log(chalk.gray(`🔄 检测到功能性组件，开始转换: ${filePath}`));
        }

        const functionalResult = await this.transformFunctionalComponent(code, filePath);
        if (functionalResult.success) {
          this.stats.astSuccess++;
          if (this.options.verbose) {
            console.log(chalk.green(`✅ 功能性组件转换成功: ${filePath}`));
          }
          return functionalResult.code;
        }
      }

      // 检查是否包含 renderContent 方法
      if (this.hasRenderContent(code)) {
        if (this.options.verbose) {
          console.log(chalk.gray(`🔄 检测到 renderContent，开始转换: ${filePath}`));
        }

        const astResult = await this.transformWithAST(code, filePath);
        if (astResult.success) {
          this.stats.astSuccess++;
          if (this.options.verbose) {
            console.log(chalk.green(`✅ AST 转换成功: ${filePath}`));
          }
          return astResult.code;
        }
      }

      this.stats.failed++;
      if (this.options.verbose) {
        console.log(chalk.yellow(`⚠️ 转换失败: ${filePath}`));
      }
      return code;
    } catch (error) {
      this.stats.failed++;
      if (this.options.verbose) {
        console.error(chalk.red(`❌ 转换异常: ${filePath}`), error.message);
      }
      return code;
    } finally {
      this.stats.total++;
    }
  }

  /**
   * 从 Vue 文件中提取 script 标签的内容
   * @param {string} code - Vue 文件代码
   * @returns {string|null} script 标签内容，如果没有找到则返回 null
   */
  extractScriptContent(code) {
    const scriptMatch = code.match(/<script[^>]*>([\s\S]*?)<\/script>/);
    if (scriptMatch) {
      return scriptMatch[1].trim();
    }
    return null;
  }

  /**
   * 重新构建 Vue 文件，将转换后的 script 内容插入回去
   * @param {string} originalVueCode - 原始 Vue 文件代码
   * @param {string} transformedScriptCode - 转换后的 script 内容
   * @returns {string} 重新构建的 Vue 文件代码
   */
  reconstructVueFile(originalVueCode, transformedScriptCode) {
    const finalScriptCode = this.ensureHImport(transformedScriptCode);
    const scriptMatch = originalVueCode.match(/(<script[^>]*>)([\s\S]*?)(<\/script>)/);
    if (scriptMatch) {
      return originalVueCode.replace(
        scriptMatch[0],
        `${scriptMatch[1]}\n${finalScriptCode}\n${scriptMatch[3]}`
      );
    }

    return finalScriptCode;
  }

  /**
   * 检查代码是否包含 renderContent 方法，匹配多种 renderContent 方法定义形式：
   * 1. renderContent(h, { node, data }) {
   * 2. renderContent: function(h, { node, data }) {
   * 3. "renderContent": function(h, { node, data }) {
   * 4. :render-content="renderContent" 在模板中
   * 5. render-content="renderContent" 在模板中
   * 6. renderContent(h, { node, data }) { return (<div>...</div>) } JSX形式
   * @param {string} code - 源代码
   * @returns {boolean} 是否包含 renderContent 方法
   */
  hasRenderContent(code) {
    const patterns = [
      // 方法定义中的 renderContent
      /renderContent\s*\(\s*h\s*,/,
      /renderContent\s*\(\s*\$_\s*,/,  // 匹配任意第一个参数名
      /renderContent\s*:\s*function\s*\(\s*h\s*,/,
      /renderContent\s*:\s*function\s*\(\s*\$_\s*,/,
      /['"]\s*renderContent\s*['"]\s*:\s*function\s*\(/,

      // JSX形式的 renderContent
      /renderContent[\s\S]*?return\s*\([\s\S]*?</,  // JSX的开始标记
      /renderContent[\s\S]*?return\s*\([\s\S]*?\)\s*\)/,  // 完整JSX表达式

      // 模板中的 render-content 属性
      /:render-content=['"]renderContent['"]/,
      /render-content=['"]renderContent['"]/,
      /:render-content=['"][\w\.]*['"]/,
      /render-content=['"][\w\.]*['"]/
    ];

    return patterns.some(pattern => pattern.test(code));
  }

  /**
   * 检查是否是功能性组件
   * @param {string} code - 源代码
   * @returns {boolean} 是否是功能性组件
   */
  isFunctionalComponent(code) {
	  // 检查是否包含 functional: true 和 render 方法
	  const hasFunctional = /functional\s*:\s*true/.test(code)
	  const hasRender = /render\s*\(\s*h\s*,\s*context\s*\)/.test(code) ||
		  /render\s*\(\s*h\s*,\s*\{\s*[^}]*\}\s*\)/.test(code) ||
		  // render: function(h, context) {
		  /render\s*:\s*function\s*\(\s*h\s*,\s*context\s*\)\s*\{/.test(code)

	  return hasFunctional && hasRender
  }

  /**
   * 检查是否是 export default function render 格式
   * @param {string} code - 源代码
   * @returns {boolean} 是否是 export default function render 格式
   */
  isExportDefaultRenderFunction(code) {
    // 检查是否包含 export default function render 模式
    const patterns = [
      /export\s+default\s+function\s+render\s*\(\s*_?props\s*,\s*_?context\s*\)/,
      /export\s+default\s+function\s+render\s*\([^)]*\)/
    ];

    return patterns.some(pattern => pattern.test(code));
  }

  /**
   * 转换功能性组件为 Vue 3 语法
   * @param {string} code - 源代码
   * @param {string} filePath - 文件路径
   * @returns {Object} 转换结果
   */
  async transformFunctionalComponent(code, filePath = '') {
    try {
      const scriptContent = this.extractScriptContent(code);
      if (!scriptContent) {
        return { success: false, error: 'No script content found' };
      }

      let ast;
      try {
        ast = gogocode(scriptContent);
      } catch (parseError) {
        if (this.options.verbose) {
          console.warn(chalk.yellow(`⚠️ 解析脚本内容失败: ${parseError.message}`));
        }
        return { success: false, error: parseError.message };
      }

      let hasChanges = false;

      // 查找 export default 对象
      const exportDefault = ast.find('export default { $$$ }');
      if (exportDefault && exportDefault.length > 0) {
        const defaultObj = exportDefault.eq(0);

        // 移除 functional: true 属性
        const functionalProp = defaultObj.find('functional: true');
        if (functionalProp && functionalProp.length > 0) {
          functionalProp.remove();
          hasChanges = true;
          if (this.options.verbose) {
            console.log(chalk.gray('  移除 functional: true'));
          }
        }

        // 查找 render 方法 - 使用多种模式匹配
        let renderMethods = [];

        // 尝试不同的 render 方法模式
        const renderPatterns = [
          'render(h, context) { $$$ }',
          'render(h, { $$$1 }) { $$$2 }',
          'render: function(h, context) { $$$ }',
          'render: function(h, { $$$1 }) { $$$2 }',
          'render($$$) { $$$ }'
        ];

        for (const pattern of renderPatterns) {
          try {
            const found = defaultObj.find(pattern);
            if (found && found.length > 0) {
              found.each(method => {
                renderMethods.push(method);
              });
              break;
            }
          } catch (error) {
            // 忽略模式匹配错误，继续下一个模式
          }
        }

        if (renderMethods.length > 0) {
          renderMethods.forEach(renderMethod => {
            try {
              // 获取 render 方法的参数
              const params = renderMethod.attr('params');
              if (params && params.length >= 2) {
                // 获取原始 render 方法的代码
                const renderCode = renderMethod.generate();

                // 转换 render 方法为 setup 方法
                const transformedSetupCode = this.convertRenderToSetup(renderCode);

                // 创建新的 setup 方法 AST
                const setupAst = gogocode(transformedSetupCode);
                const setupMethod = setupAst.find('setup($$$) { $$$ }');

                if (setupMethod && setupMethod.length > 0) {
                  // 替换 render 方法为 setup 方法
                  renderMethod.replaceBy(setupMethod.eq(0));
                  hasChanges = true;

                  if (this.options.verbose) {
                    console.log(chalk.gray('  转换 render 方法为 setup 方法'));
                  }
                }
              }
            } catch (error) {
              if (this.options.verbose) {
                console.warn(chalk.yellow(`⚠️ 转换单个 render 方法失败: ${error.message}`));
              }
            }
          });
        }
      }

      if (hasChanges) {
        // 转换 JSX 为 h() 函数调用
        const jsxTransformedAst = this.transformJSXToH(ast);

        let transformedScriptCode;
        if (jsxTransformedAst && typeof jsxTransformedAst.generate === 'function') {
          transformedScriptCode = jsxTransformedAst.generate();
        } else {
          transformedScriptCode = ast.generate();
        }

        // 确保添加 h 导入
        const finalScriptCode = this.ensureHImport(transformedScriptCode);
        const finalCode = this.reconstructVueFile(code, finalScriptCode);

        return {
          success: true,
          code: finalCode
        };
      }

      return { success: false, error: 'No functional component changes made' };
    } catch (error) {
      if (this.options.verbose) {
        console.warn(chalk.yellow(`⚠️ 功能性组件转换失败: ${error.message}`));
      }
      return { success: false, error: error.message };
    }
  }

  /**
   * 转换 export default function render 为 Vue 3 语法
   * @param {string} code - 源代码
   * @param {string} filePath - 文件路径
   * @returns {Object} 转换结果
   */
  async transformExportDefaultRenderFunction(code, filePath = '') {
    try {
      const scriptContent = this.extractScriptContent(code);
      if (!scriptContent) {
        return { success: false, error: 'No script content found' };
      }

      // 使用正则表达式匹配 export default function render
      const functionMatch = scriptContent.match(
        /export\s+default\s+function\s+render\s*\(([^)]*)\)\s*\{([\s\S]*)\}$/
      );

      if (!functionMatch) {
        return { success: false, error: 'No export default function render found' };
      }

      const [, params, functionBody] = functionMatch;

      // 解析参数
      const paramList = params.split(',').map(p => p.trim());
      let propsParam = '_props';
      let contextParam = '_context';

      if (paramList.length >= 2) {
        propsParam = paramList[0];
        contextParam = paramList[1];
      }

      // 转换函数体
      let transformedBody = this.transformExportRenderFunctionBody(
        functionBody,
        propsParam,
        contextParam
      );

      // 转换 JSX 为 h() 函数调用
      transformedBody = this.convertJSXInCode(transformedBody);

      // 构建新的 setup 组件
      const setupComponent = `import { h } from 'vue'

export default {
  setup(props, { slots, emit, attrs }) {
    return () => {${transformedBody}
    }
  }
}`;

      const finalCode = this.reconstructVueFile(code, setupComponent);

      return {
        success: true,
        code: finalCode
      };
    } catch (error) {
      if (this.options.verbose) {
        console.warn(chalk.yellow(`⚠️ export default function render 转换失败: ${error.message}`));
      }
      return { success: false, error: error.message };
    }
  }

  /**
   * 转换 export default function render 的函数体
   * @param {string} functionBody - 函数体代码
   * @param {string} propsParam - props 参数名
   * @param {string} contextParam - context 参数名
   * @returns {string} 转换后的函数体
   */
  transformExportRenderFunctionBody(functionBody, propsParam, contextParam) {
    let transformedBody = functionBody;

    // 移除手动构建的 context 对象
    const contextBuildPattern = new RegExp(
      `const\\s+context\\s*=\\s*\\{[\\s\\S]*?${propsParam}[\\s\\S]*?\\}`,
      'g'
    );
    transformedBody = transformedBody.replace(contextBuildPattern, '');

    // 转换 context.props 访问为直接的 props 访问
    transformedBody = transformedBody.replace(/context\.props/g, 'props');
    transformedBody = transformedBody.replace(/context\.data/g, 'attrs');
    transformedBody = transformedBody.replace(/context\.children/g, 'slots');
    transformedBody = transformedBody.replace(/context\.slots/g, 'slots');
    transformedBody = transformedBody.replace(/context\.emit/g, 'emit');

    // 替换原始参数引用
    if (propsParam !== 'props') {
      const propsParamRegex = new RegExp(`\\b${propsParam}\\b`, 'g');
      transformedBody = transformedBody.replace(propsParamRegex, 'props');
    }

    if (contextParam !== 'context') {
      const contextParamRegex = new RegExp(`\\b${contextParam}\\b`, 'g');
      transformedBody = transformedBody.replace(contextParamRegex, 'context');
    }

    // 清理多余的空行
    transformedBody = transformedBody.replace(/\n\s*\n\s*\n/g, '\n\n');

    return transformedBody;
  }

  /**
   * 转换变量声明，处理 context.props 访问
   * @param {Object} stmt - 变量声明语句
   * @returns {string} 转换后的代码
   */
  transformVariableDeclaration(stmt) {
    try {
      let code = gogocode(stmt).generate();

      // 转换 context.props 为 props
      code = code.replace(/context\.props/g, 'props');
      // 转换 context.slots 为 slots
      code = code.replace(/context\.slots/g, 'slots');
      // 转换 context.emit 为 emit
      code = code.replace(/context\.emit/g, 'emit');
      // 转换 context.attrs 为 attrs
      code = code.replace(/context\.attrs/g, 'attrs');

      return code;
    } catch (error) {
      return gogocode(stmt).generate();
    }
  }

  /**
   * 转换返回语句
   * @param {Object} stmt - 返回语句
   * @returns {string} 转换后的代码
   */
  transformReturnStatement(stmt) {
    try {
      let code = gogocode(stmt).generate();

      // 转换 context 相关的访问
      code = code.replace(/context\.props/g, 'props');
      code = code.replace(/context\.slots/g, 'slots');
      code = code.replace(/context\.emit/g, 'emit');
      code = code.replace(/context\.attrs/g, 'attrs');

      return code;
    } catch (error) {
      return gogocode(stmt).generate();
    }
  }

  /**
   * 检查代码是否包含 renderContent 方法，匹配多种 renderContent 方法定义形式：
   * 1. renderContent(h, { node, data }) {
   * 2. renderContent: function(h, { node, data }) {
   * 3. "renderContent": function(h, { node, data }) {
   * 4. :render-content="renderContent" 在模板中
   * 5. render-content="renderContent" 在模板中
   * 6. renderContent(h, { node, data }) { return (<div>...</div>) } JSX形式
   * @param {string} code - 源代码
   * @returns {boolean} 是否包含 renderContent 方法
   */
  hasRenderContent(code) {
    const patterns = [
      // 方法定义中的 renderContent
      /renderContent\s*\(\s*h\s*,/,
      /renderContent\s*\(\s*\$_\s*,/,  // 匹配任意第一个参数名
      /renderContent\s*:\s*function\s*\(\s*h\s*,/,
      /renderContent\s*:\s*function\s*\(\s*\$_\s*,/,
      /['"]\s*renderContent\s*['"]\s*:\s*function\s*\(/,

      // JSX形式的 renderContent
      /renderContent[\s\S]*?return\s*\([\s\S]*?</,  // JSX的开始标记
      /renderContent[\s\S]*?return\s*\([\s\S]*?\)\s*\)/,  // 完整JSX表达式

      // 模板中的 render-content 属性
      /:render-content=['"]renderContent['"]/,
      /render-content=['"]renderContent['"]/,
      /:render-content=['"][\w\.]*['"]/,
      /render-content=['"][\w\.]*['"]/
    ];

    return patterns.some(pattern => pattern.test(code));
  }

  async transformWithAST(code, filePath) {
    try {
      // 如果有传入的 AST，使用它；否则解析代码
      let ast = this.ast;
      if (!ast) {
        const scriptContent = this.extractScriptContent(code);
        if (scriptContent) {
          try {
            ast = gogocode(scriptContent);
          } catch (parseError) {
            if (this.options.verbose) {
              console.warn(chalk.yellow(`⚠️ 解析脚本内容失败，尝试完整解析: ${parseError.message}`));
            }
            ast = gogocode(code);
          }
        } else {
          ast = gogocode(code);
        }
      }

      // 确保 AST 是有效的 gogocode 对象
      let transformedAst;
      if (ast && typeof ast.find === 'function') {
        transformedAst = ast;
      } else {
        // 如果传入的 AST 无效，重新解析代码
        const scriptContent = this.extractScriptContent(code);
        if (scriptContent) {
          try {
            transformedAst = gogocode(scriptContent);
          } catch (parseError) {
            if (this.options.verbose) {
              console.warn(chalk.yellow(`⚠️ 重新解析脚本内容失败: ${parseError.message}`));
            }
            transformedAst = gogocode(code);
          }
        } else {
          transformedAst = gogocode(code);
        }
      }

      let hasChanges = false;

      // 尝试多种搜索模式来查找 renderContent 方法
      const searchPatterns = [
        'renderContent(h, { node, data }) { $$$ }',
        'renderContent($_, { node, data }) { $$$ }',
        'renderContent(h, $_) { $$$ }',  // 更通用的参数模式
        'renderContent($_, $_) { $$$ }',  // 任意两个参数
        'renderContent: function(h, { node, data }) { $$$ }',
        'renderContent: function($_, { node, data }) { $$$ }',
        'renderContent: function(h, $_) { $$$ }',
        'renderContent: function($_, $_) { $$$ }'
      ];

      let foundMethods = [];
      for (const pattern of searchPatterns) {
        try {
          const methods = transformedAst.find(pattern);
          if (methods && methods.length > 0) {
            foundMethods = [];
            methods.each(method => {
              foundMethods.push(method);
            });
            if (this.options.verbose) {
              console.log(chalk.gray(`  找到 renderContent 方法，使用模式: ${pattern}`));
            }
            break;
          }
        } catch (patternError) {
          // 忽略模式错误，尝试下一个
        }
      }

      // 如果没有找到，尝试更通用的搜索
      if (foundMethods.length === 0) {
        try {
          // 确保 transformedAst 仍然有效
          if (!transformedAst || typeof transformedAst.find !== 'function') {
            transformedAst = gogocode(code);
          }

          // 查找所有方法定义
          const allMethods = transformedAst.find('{ renderContent: function($$$) { $$$ } }');
          if (allMethods && allMethods.length > 0) {
            allMethods.each(methodNode => {
              const renderContentProperty = methodNode.find('renderContent: function($$$) { $$$ }');
              if (renderContentProperty && renderContentProperty.length > 0) {
                renderContentProperty.each(prop => {
                  foundMethods.push(prop);
                });
              }
            });
          }

          // 尝试查找函数表达式
          if (foundMethods.length === 0) {
            const funcExpressions = transformedAst.find('renderContent($$$) { $$$ }');
            if (funcExpressions && funcExpressions.length > 0) {
              funcExpressions.each(funcNode => {
                const params = funcNode.attr('params');
                if (params && params.length >= 1) {  // 至少有一个参数
                  // 检查是否有 JSX 返回语句
                  const body = funcNode.attr('body');
                  if (body && body.type === 'BlockStatement') {
                    const returnStatements = body.body.filter(stmt => stmt.type === 'ReturnStatement');
                    if (returnStatements.length > 0) {
                      const returnArg = returnStatements[0].argument;
                      // 检查是否是 JSX 或括号表达式
                      if (returnArg &&
                          (returnArg.type === 'JSXElement' ||
                           (returnArg.type === 'ParenthesizedExpression' &&
                            returnArg.expression &&
                            returnArg.expression.type === 'JSXElement'))) {
                        foundMethods.push(funcNode);
                      }
                    }
                  }

                  // 如果没有找到 JSX，检查参数模式
                  if (!foundMethods.includes(funcNode) && params.length >= 2) {
                    const firstParam = params[0];
                    const secondParam = params[1];

                    // 检查第一个参数是否是 h 或类似的渲染函数
                    if (firstParam && firstParam.type === 'Identifier') {
                      foundMethods.push(funcNode);
                    }
                  }
                }
              });
            }
          }

          // 尝试查找 JSX 返回语句
          if (foundMethods.length === 0) {
            try {
              // 使用正则表达式查找 JSX 返回语句
              const jsxPattern = /renderContent\s*\([^\)]*\)\s*{[\s\S]*?return\s*\([\s\S]*?<[\s\S]*?>[\s\S]*?<\/[\s\S]*?>[\s\S]*?\)[\s\S]*?}/g;
              const jsxMatches = code.match(jsxPattern);

              if (jsxMatches && jsxMatches.length > 0) {
                // 找到了 JSX 返回语句，尝试解析
                for (const jsxMatch of jsxMatches) {
                  try {
                    const matchAst = gogocode(jsxMatch);
                    const funcExpr = matchAst.find('renderContent($$$) { $$$ }');
                    if (funcExpr && funcExpr.length > 0) {
                      funcExpr.each(node => {
                        foundMethods.push(node);
                      });
                    }
                  } catch (jsxError) {
                    if (this.options.verbose) {
                      console.warn(chalk.yellow(`⚠️ 解析 JSX 匹配失败: ${jsxError.message}`));
                    }
                  }
                }
              }
            } catch (regexError) {
              if (this.options.verbose) {
                console.warn(chalk.yellow(`⚠️ 正则表达式搜索失败: ${regexError.message}`));
              }
            }
          }
        } catch (genericError) {
          if (this.options.verbose) {
            console.warn(chalk.yellow(`⚠️ 通用搜索失败: ${genericError.message}`));
          }
        }
      }

      if (foundMethods.length > 0) {
        foundMethods.forEach(node => {
          try {
            const params = node.attr('params');
            if (params && params.length >= 1) {  // 至少有一个参数
              // 转换方法签名：移除第一个参数 h
              const newParams = params.slice(1); // 移除第一个参数 h
              node.attr('params', newParams);

              hasChanges = true;

              if (this.options.verbose) {
                console.log(chalk.gray('  转换 renderContent 方法签名'));
              }
            }
          } catch (error) {
            if (this.options.verbose) {
              console.warn(chalk.yellow(`⚠️ 转换单个 renderContent 方法失败: ${error.message}`));
            }
          }
        });
      }

      if (hasChanges) {
        // 转换 JSX 为 h() 函数调用
        const jsxTransformedAst = this.transformJSXToH(transformedAst);

        // 检查转换后的 AST 是否有效
        if (jsxTransformedAst && typeof jsxTransformedAst.generate === 'function') {
          const transformedScriptCode = jsxTransformedAst.generate();
          const finalCode = this.reconstructVueFile(code, transformedScriptCode);

          return {
            success: true,
            code: finalCode
          };
        } else {
          // 如果 JSX 转换失败，使用原始的转换结果
          const transformedScriptCode = transformedAst.generate();
          const finalCode = this.reconstructVueFile(code, transformedScriptCode);

          return {
            success: true,
            code: finalCode
          };
        }
      }

      // 如果没有找到方法但代码中包含 renderContent，尝试使用正则表达式转换
      if (!hasChanges && this.hasRenderContent(code)) {
        if (this.options.verbose) {
          console.log(chalk.gray('  使用正则表达式尝试转换 renderContent'));
        }

        // 使用正则表达式替换 renderContent(h, ...) 为 renderContent(...)
        const regexTransformed = code.replace(
          /renderContent\s*\(\s*h\s*,\s*/g,
          'renderContent('
        );

        if (regexTransformed !== code) {
          // 确保添加 h 导入
          const finalCode = this.ensureHImport(regexTransformed);

          return {
            success: true,
            code: finalCode
          };
        }
      }

      return { success: false, error: 'No renderContent methods found or transformed' };
    } catch (error) {
      if (this.options.verbose) {
        console.warn(chalk.yellow(`⚠️ AST 转换失败，回退到正则方式: ${error.message}`));
      }

      return { success: false, error: error.message };
    }
  }

  /**
   * 转换 AST 中的 JSX 为 h() 函数调用
   * @param {Object} ast - gogocode AST 对象
   * @returns {Object} 转换后的 AST
   */
  transformJSXToH(ast) {
    try {
      // 获取当前代码
      let code = ast.generate();
      let hasJSXChanges = false;

      // 查找所有 renderContent 方法中的 return 语句
      const returnStatements = ast.find('return $$$');

      if (returnStatements && returnStatements.length > 0) {
        returnStatements.each(returnStmt => {
          const returnArg = returnStmt.attr('argument');
          if (returnArg && returnArg.type === 'JSXElement') {
            try {
              const transformedH = this.convertJSXToHCode(returnArg);
              if (transformedH) {
                // 使用更精确的方法匹配完整的 return 语句
                const returnMatch = this.findCompleteReturnStatement(code, returnArg);

                if (returnMatch) {
                  const replacement = `return ${transformedH}`;
                  code = code.replace(returnMatch, replacement);
                  hasJSXChanges = true;

                  if (this.options.verbose) {
                    console.log(chalk.gray(`  转换 JSX 为 h() 函数调用`));
                  }
                }
              }
            } catch (jsxError) {
              if (this.options.verbose) {
                console.warn(chalk.yellow(`⚠️ 转换返回语句中的 JSX 失败: ${jsxError.message}`));
              }
            }
          }
        });
      }

      // 如果有 JSX 变化，重新解析代码；否则返回原始 AST
      if (hasJSXChanges) {
        try {
          return gogocode(code);
        } catch (parseError) {
          if (this.options.verbose) {
            console.warn(chalk.yellow(`⚠️ 重新解析代码失败，返回原始 AST: ${parseError.message}`));
          }
          return ast;
        }
      } else {
        return ast;
      }
    } catch (error) {
      if (this.options.verbose) {
        console.warn(chalk.yellow(`⚠️ JSX 转换失败: ${error.message}`));
      }
      return ast;
    }
  }

  /**
   * 检查匹配的 return 语句是否包含目标 JSX
   * @param {string} returnCode - return 语句代码
   * @param {Object} targetJSX - 目标 JSX 节点
   * @returns {boolean} 是否包含目标 JSX
   */
  containsTargetJSX(returnCode, targetJSX) {
    try {
      // 简单检查：如果 return 语句包含目标 JSX 的标签名，就认为匹配
      const tagName = targetJSX.openingElement?.name?.name;
      if (tagName) {
        return returnCode.includes(`<${tagName}`);
      }
      return false;
    } catch (error) {
      return false;
    }
  }

  /**
   * 转换 Vue 2 事件名到 Vue 3 格式
   * @param {string} name - 属性名
   * @returns {string} 转换后的属性名
   */
  convertEventName(name) {
    // 转换 on-click 到 onClick
    if (name.startsWith('on-')) {
      const eventName = name.substring(3); // 移除 'on-'
      return 'on' + eventName.charAt(0).toUpperCase() + eventName.slice(1);
    }
    return name;
  }

  /**
   * 构建 props 对象字符串，正确处理事件处理函数
   * @param {Object} props - 属性对象
   * @returns {string} props 对象字符串
   */
  buildPropsString(props) {
    const propPairs = [];

    for (const [key, value] of Object.entries(props)) {
      let valueStr;

      // 检查是否是事件处理函数
      if (key.startsWith('on') && key.length > 2 && key[2] === key[2].toUpperCase()) {
        // 事件处理函数，不用引号包围
        valueStr = value;
      } else if (typeof value === 'string') {
        // 字符串值，用引号包围
        valueStr = JSON.stringify(value);
      } else if (typeof value === 'object') {
        // 对象值，递归处理
        valueStr = JSON.stringify(value);
      } else {
        // 其他类型
        valueStr = JSON.stringify(value);
      }

      propPairs.push(`${JSON.stringify(key)}: ${valueStr}`);
    }

    return `{ ${propPairs.join(', ')} }`;
  }

  /**
   * 构建格式化的 props 对象字符串
   * @param {Object} props - 属性对象
   * @param {number} indentLevel - 缩进级别
   * @returns {string} 格式化的 props 对象字符串
   */
  buildPropsStringFormatted(props, indentLevel = 0) {
    const propPairs = [];
    const indent = '  '.repeat(indentLevel);

    for (const [key, value] of Object.entries(props)) {
      let valueStr;

      // 检查是否是事件处理函数
      if (key.startsWith('on') && key.length > 2 && key[2] === key[2].toUpperCase()) {
        // 事件处理函数，不用引号包围
        valueStr = value;
      } else if (typeof value === 'string') {
        // 字符串值，用引号包围
        valueStr = JSON.stringify(value);
      } else if (typeof value === 'object') {
        // 对象值，格式化处理
        if (Object.keys(value).length <= 2) {
          valueStr = JSON.stringify(value);
        } else {
          // 复杂对象使用多行格式
          const objPairs = Object.entries(value).map(([k, v]) =>
            `${indent}  ${JSON.stringify(k)}: ${JSON.stringify(v)}`
          );
          valueStr = `{\n${objPairs.join(',\n')}\n${indent}}`;
        }
      } else {
        // 其他类型
        valueStr = JSON.stringify(value);
      }

      propPairs.push(`${JSON.stringify(key)}: ${valueStr}`);
    }

    // 如果属性较少且简单，使用单行格式
    const singleLine = `{ ${propPairs.join(', ')} }`;
    if (propPairs.length <= 2 && singleLine.length < 50) {
      return singleLine;
    }

    // 使用多行格式
    return `{\n${propPairs.map(pair => `${indent}  ${pair}`).join(',\n')}\n${indent}}`;
  }

  /**
   * 查找完整的 return 语句，使用括号计数确保匹配完整
   * @param {string} code - 源代码
   * @param {Object} targetJSX - 目标 JSX 节点
   * @returns {string|null} 完整的 return 语句，如果没找到则返回 null
   */
  findCompleteReturnStatement(code, targetJSX) {
    try {
      const tagName = targetJSX.openingElement?.name?.name;
      if (!tagName) return null;

      // 查找所有 "return (" 的位置
      const returnPattern = /return\s*\(/g;
      let match;

      while ((match = returnPattern.exec(code)) !== null) {
        const startIndex = match.index;
        const openParenIndex = match.index + match[0].length - 1;

        // 从开括号开始计数，找到匹配的闭括号
        let parenCount = 1;
        let currentIndex = openParenIndex + 1;

        while (currentIndex < code.length && parenCount > 0) {
          const char = code[currentIndex];
          if (char === '(') {
            parenCount++;
          } else if (char === ')') {
            parenCount--;
          }
          currentIndex++;
        }

        if (parenCount === 0) {
          // 找到了匹配的闭括号
          const returnStatement = code.substring(startIndex, currentIndex);

          // 检查这个 return 语句是否包含目标 JSX
          if (returnStatement.includes(`<${tagName}`)) {
            return returnStatement;
          }
        }
      }

      return null;
    } catch (error) {
      if (this.options.verbose) {
        console.warn(chalk.yellow(`⚠️ 查找完整 return 语句失败: ${error.message}`));
      }
      return null;
    }
  }

  convertJSXToHCode(jsxNode, indentLevel = 0) {
    try {
      const tagName = jsxNode.openingElement?.name?.name;
      if (!tagName) {
        return null;
      }

      // 调试输出（仅在详细模式下）
      if (this.options.verbose && this.options.debug) {
        console.log(chalk.gray(`  调试 JSX 节点: ${tagName}`));
        console.log(chalk.gray(`  节点类型: ${jsxNode.type}`));
        if (jsxNode.openingElement?.attributes) {
          console.log(chalk.gray(`  属性数量: ${jsxNode.openingElement.attributes.length}`));
          jsxNode.openingElement.attributes.forEach((attr, index) => {
            console.log(chalk.gray(`    属性 ${index}: ${attr.name?.name} = ${attr.value?.type} (${JSON.stringify(attr.value?.value || attr.value?.raw)})`));
          });
        }
      }

      const props = this.extractJSXPropsFromNode(jsxNode);
      const children = this.extractJSXChildrenFromNode(jsxNode, indentLevel + 1);

      return this.formatHCall(tagName, props, children, indentLevel);
    } catch (error) {
      if (this.options.verbose) {
        console.warn(chalk.yellow(`⚠️ JSX 转 h() 代码失败: ${error.message}`));
      }
      return null;
    }
  }

  /**
   * 格式化 h() 函数调用，使其更易读
   * @param {string} tagName - 标签名
   * @param {Object} props - 属性对象
   * @param {Array} children - 子元素数组
   * @param {number} indentLevel - 缩进级别
   * @returns {string} 格式化的 h() 调用
   */
  formatHCall(tagName, props, children, indentLevel = 0) {
    const indent = '  '.repeat(indentLevel);
    const nextIndent = '  '.repeat(indentLevel + 1);

    const hasProps = props && Object.keys(props).length > 0;
    const hasChildren = children && children.length > 0;

    // 计算总复杂度来决定格式
    const propsComplexity = hasProps ? Object.keys(props).length : 0;
    const childrenComplexity = hasChildren ? children.length : 0;
    const hasNestedH = hasChildren && children.some(child =>
      typeof child === 'string' && child.includes('h(')
    );

    // 简单的单行格式条件
    const isSimple = !hasNestedH && propsComplexity <= 1 && childrenComplexity <= 1;

    if (isSimple) {
      // 简单单行格式
      let result = `h('${tagName}'`;

      if (hasProps) {
        result += `, ${this.buildPropsString(props)}`;
      } else {
        result += ', null';
      }

      if (hasChildren) {
        result += `, ${children[0]}`;
      }

      result += ')';
      return result;
    }

    // 复杂多行格式
    let result = `h('${tagName}',`;

    // 添加属性
    if (hasProps) {
      const propsStr = this.buildPropsStringFormatted(props, indentLevel);
      result += `\n${nextIndent}${propsStr}`;
    } else {
      result += `\n${nextIndent}null`;
    }

    // 添加子元素
    if (hasChildren) {
      if (children.length === 1) {
        const child = children[0];
        result += `,\n${nextIndent}${child}`;
      } else {
        result += `,\n${nextIndent}[\n`;
        children.forEach((child, index) => {
          const childIndent = nextIndent + '  ';
          result += `${childIndent}${child}`;
          if (index < children.length - 1) {
            result += ',';
          }
          result += '\n';
        });
        result += `${nextIndent}]`;
      }
    }

    result += '\n' + indent + ')';
    return result;
  }

  extractJSXPropsFromNode(jsxNode) {
    const props = {};

    try {
      const attributes = jsxNode.openingElement?.attributes || [];

      attributes.forEach(attr => {
        if (attr.type === 'JSXAttribute') {
          let name = attr.name?.name;
          if (!name) return;

          // 转换 Vue 2 事件名到 Vue 3 格式
          name = this.convertEventName(name);

          const value = attr.value;

          if (value) {
            if (value.type === 'Literal' || value.type === 'StringLiteral') {
              // 直接的字符串值，如 class="custom-tree-node"
              if (name === 'style' && typeof value.value === 'string') {
                // 将 style 字符串转换为对象
                props[name] = this.parseInlineStyle(value.value);
              } else {
                props[name] = value.value;
              }
            } else if (value.type === 'JSXExpressionContainer') {
              const expression = value.expression;
              if (expression.type === 'Literal') {
                props[name] = expression.value;
              } else if (expression.type === 'ObjectExpression') {
                // 处理对象表达式（如 style）
                const objProps = {};
                if (expression.properties) {
                  expression.properties.forEach(prop => {
                    if (prop.type === 'Property' && prop.key && prop.value) {
                      const key = prop.key.name || prop.key.value;
                      let val = prop.value.value;

                      // 处理数值类型
                      if (prop.value.type === 'Literal') {
                        val = prop.value.value;
                      } else if (prop.value.type === 'Identifier') {
                        val = prop.value.name;
                      }

                      if (key && val !== undefined) {
                        objProps[key] = val;
                      }
                    }
                  });
                }
                props[name] = objProps;
              } else {
                // 对于其他表达式，如果是事件处理函数，直接使用表达式
                if (name.startsWith('on') && name.length > 2 && name[2] === name[2].toUpperCase()) {
                  // 这是一个事件处理函数
                  props[name] = this.generateExpressionCode(expression);
                } else {
                  // 对于其他表达式，包装在大括号中
                  props[name] = `{${this.generateExpressionCode(expression)}}`;
                }
              }
            } else if (value.type === 'JSXFragment') {
              // 处理 JSX 片段
              props[name] = this.generateExpressionCode(value);
            }
          } else {
            // 布尔属性
            props[name] = true;
          }
        }
      });

      // 调试输出（仅在调试模式下）
      if (this.options.verbose && this.options.debug && Object.keys(props).length > 0) {
        console.log(chalk.gray(`  提取到的属性: ${JSON.stringify(props)}`));
      }
    } catch (error) {
      if (this.options.verbose) {
        console.warn(chalk.yellow(`⚠️ 提取 JSX 属性失败: ${error.message}`));
      }
    }

    return props;
  }

  extractJSXChildrenFromNode(jsxNode, indentLevel = 0) {
    const children = [];

    try {
      const jsxChildren = jsxNode.children || [];

      jsxChildren.forEach(child => {
        if (child.type === 'JSXText') {
          const text = child.value ? child.value.trim() : '';
          if (text) {
            children.push(`'${text.replace(/'/g, "\\'")}'`);
          }
        } else if (child.type === 'JSXExpressionContainer') {
          const expression = child.expression;
          if (expression.type === 'Identifier') {
            children.push(expression.name);
          } else if (expression.type === 'Literal') {
            children.push(JSON.stringify(expression.value));
          } else {
            children.push(this.generateExpressionCode(expression));
          }
        } else if (child.type === 'JSXElement') {
          const nestedH = this.convertJSXToHCode(child, indentLevel);
          if (nestedH) {
            children.push(nestedH);
          }
        }
      });
    } catch (error) {
      if (this.options.verbose) {
        console.warn(chalk.yellow(`⚠️ 提取 JSX 子元素失败: ${error.message}`));
      }
    }

    return children;
  }

  generateExpressionCode (expression) {
    try {
      switch (expression.type) {
        case 'Identifier':
          return expression.name
        case 'MemberExpression':
          const object = this.generateExpressionCode(expression.object)
          const property = expression.computed
            ? `[${this.generateExpressionCode(expression.property)}]`
            : `.${expression.property.name}`
          return `${object}${property}`
        case 'Literal':
          return JSON.stringify(expression.value)
        case 'BooleanLiteral':
          return expression.value ? 'true' : 'false'
        case 'StringLiteral':
          return JSON.stringify(expression.value)
        case 'ArrowFunctionExpression': // 处理箭头函数
          const params = expression.params ? expression.params.map(p => p.name || p).join(', ') : ''
          const body = this.generateFunctionBody(expression.body)
          return `(${params}) => ${body}`
        case 'CallExpression': // 处理函数调用
          const callee = this.generateExpressionCode(expression.callee)
          const args = expression.arguments ? expression.arguments.map(arg => this.generateExpressionCode(arg)).join(', ') : ''
          return `${callee}(${args})`
        case 'ThisExpression':
          return 'this'
        default:
          return `/* ${expression.type} */`
      }
    } catch (error) {
      return `/* error: ${error.message} */`
    }
  }

  generateFunctionBody(body) {
    try {
      if (body.type === 'BlockStatement') {
        // 对于块语句，简化处理
        return '{ /* function body */ }';
      } else {
        // 对于表达式体
        return this.generateExpressionCode(body);
      }
    } catch (error) {
      return '{ /* error */ }';
    }
  }

  convertJSXToH(jsxNode) {
    try {
      const openingElement = jsxNode.attr('openingElement');
      if (!openingElement) {
        return null;
      }

      const tagName = openingElement.name ? openingElement.name.name : null;
      if (!tagName) {
        return null;
      }

      const props = this.extractJSXProps(jsxNode);
      const children = this.extractJSXChildren(jsxNode);

      // 构建 h() 函数调用
      let hCall = `h('${tagName}'`;

      if (props && Object.keys(props).length > 0) {
        hCall += `, ${JSON.stringify(props)}`;
      } else {
        hCall += ', null';
      }

      if (children.length > 0) {
        if (children.length === 1) {
          hCall += `, ${children[0]}`;
        } else {
          hCall += `, [${children.join(', ')}]`;
        }
      }

      hCall += ')';

      // 创建新的 AST 节点并返回
      const hAST = gogocode(hCall, { isProgram: false });
      const callExpression = hAST.find('CallExpression');
      if (callExpression && callExpression.length > 0) {
        return callExpression.eq(0);
      }

      return null;
    } catch (error) {
      if (this.options.verbose) {
        console.warn(chalk.yellow(`⚠️ JSX 转 h() 失败: ${error.message}`));
      }
      return null;
    }
  }

  extractJSXProps(jsxNode) {
    const props = {};

    try {
      const openingElement = jsxNode.attr('openingElement');
      if (!openingElement) {
        return props;
      }

      const attributes = openingElement.attributes || [];

      attributes.forEach(attr => {
        if (attr.type === 'JSXAttribute') {
          const name = attr.name ? attr.name.name : null;
          if (!name) return;

          const value = attr.value;

          if (value) {
            if (value.type === 'Literal') {
              props[name] = value.value;
            } else if (value.type === 'JSXExpressionContainer') {
              // 处理 JSX 表达式
              const expression = value.expression;
              if (expression.type === 'Literal') {
                props[name] = expression.value;
              } else if (expression.type === 'ObjectExpression') {
                // 处理对象表达式（如 style）
                const objProps = {};
                if (expression.properties) {
                  expression.properties.forEach(prop => {
                    if (prop.type === 'Property' && prop.key && prop.value) {
                      const key = prop.key.name || prop.key.value;
                      const val = prop.value.value;
                      if (key && val !== undefined) {
                        objProps[key] = val;
                      }
                    }
                  });
                }
                props[name] = objProps;
              } else {
                // 对于其他表达式，尝试转换为字符串
                try {
                  const expressionCode = gogocode(expression).generate();
                  props[name] = expressionCode;
                } catch (e) {
                  // 如果无法转换，跳过此属性
                  if (this.options.verbose) {
                    console.warn(chalk.yellow(`⚠️ 无法转换 JSX 表达式: ${e.message}`));
                  }
                }
              }
            }
          } else {
            // 布尔属性
            props[name] = true;
          }
        }
      });
    } catch (error) {
      if (this.options.verbose) {
        console.warn(chalk.yellow(`⚠️ 提取 JSX 属性失败: ${error.message}`));
      }
    }

    return props;
  }

  extractJSXChildren(jsxNode) {
    const children = [];

    try {
      const jsxChildren = jsxNode.attr('children') || [];

      jsxChildren.forEach(child => {
        if (child.type === 'JSXText') {
          const text = child.value ? child.value.trim() : '';
          if (text) {
            children.push(`'${text.replace(/'/g, "\\'")}'`);
          }
        } else if (child.type === 'JSXExpressionContainer') {
          // 处理 JSX 表达式
          const expression = child.expression;
          if (expression.type === 'Identifier') {
            children.push(expression.name);
          } else if (expression.type === 'MemberExpression') {
            try {
              const memberCode = gogocode(expression).generate();
              children.push(memberCode);
            } catch (e) {
              if (this.options.verbose) {
                console.warn(chalk.yellow(`⚠️ 无法生成成员表达式代码: ${e.message}`));
              }
            }
          } else if (expression.type === 'Literal') {
            children.push(JSON.stringify(expression.value));
          } else {
            // 对于其他表达式，尝试生成代码
            try {
              const expressionCode = gogocode(expression).generate();
              children.push(expressionCode);
            } catch (e) {
              if (this.options.verbose) {
                console.warn(chalk.yellow(`⚠️ 无法生成表达式代码: ${e.message}`));
              }
            }
          }
        } else if (child.type === 'JSXElement') {
          const nestedH = this.convertJSXToH(child);
          if (nestedH) {
            try {
              const nestedCode = nestedH.generate();
              children.push(nestedCode);
            } catch (e) {
              if (this.options.verbose) {
                console.warn(chalk.yellow(`⚠️ 无法生成嵌套 JSX 代码: ${e.message}`));
              }
            }
          }
        }
      });
    } catch (error) {
      if (this.options.verbose) {
        console.warn(chalk.yellow(`⚠️ 提取 JSX 子元素失败: ${error.message}`));
      }
    }

    return children;
  }

  /**
   * 将 render 方法转换为 setup 方法
   * @param {string} renderCode - render 方法的代码
   * @returns {string} setup 方法的代码
   */
  convertRenderToSetup(renderCode) {
    try {
      // 提取 render 方法的函数体
      const renderMatch = renderCode.match(/render\s*(?:\:\s*function)?\s*\([^)]*\)\s*\{([\s\S]*)\}$/);
      if (!renderMatch) {
        return renderCode;
      }

      let renderBody = renderMatch[1];

      // 转换 context 相关的访问
      renderBody = renderBody.replace(/context\.props/g, 'props');
      renderBody = renderBody.replace(/context\.slots/g, 'slots');
      renderBody = renderBody.replace(/context\.emit/g, 'emit');
      renderBody = renderBody.replace(/context\.attrs/g, 'attrs');

      // 转换 JSX 元素为 h() 函数调用
      renderBody = this.convertJSXInCode(renderBody);

      // 构建 setup 方法
      const setupCode = `setup(props, { slots, emit, attrs }) {
    return () => {${renderBody}
    }
  }`;

      return setupCode;
    } catch (error) {
      if (this.options.verbose) {
        console.warn(chalk.yellow(`⚠️ 转换 render 为 setup 失败: ${error.message}`));
      }
      return renderCode;
    }
  }

  /**
   * 在代码字符串中转换 JSX 为 h() 函数调用
   * @param {string} code - 包含 JSX 的代码
   * @returns {string} 转换后的代码
   */
  convertJSXInCode(code) {
    try {
      // 处理多行 JSX return 语句，例如：
      // return (
      //   <div class="menu-item">
      //     {vnodes}
      //   </div>
      // )
      code = code.replace(/return\s*\(\s*<([\w-]+)\s+([^>]*?)>\s*\{([^}]+)\}\s*<\/\1>\s*\)/g, (match, tagName, attributes, content) => {
        const props = this.parseJSXAttributes(attributes);

        const propsStr = Object.keys(props).length > 0 ?
          `{ ${Object.entries(props).map(([k, v]) => `"${k}": ${v}`).join(', ')} }` :
          'null';

        return `return h('${tagName}', ${propsStr}, ${content.trim()})`;
      });

      // 处理带属性的标签 <tag prop={value}>content</tag>
      code = code.replace(/<([\w-]+)\s+([^>]*?)>([^<]*?)<\/\1>/g, (match, tagName, attributes, content) => {
        const props = this.parseJSXAttributes(attributes);

        const propsStr = Object.keys(props).length > 0 ?
          `{ ${Object.entries(props).map(([k, v]) => `"${k}": ${v}`).join(', ')} }` :
          'null';

        const trimmedContent = content.trim();
        let contentStr = '';

        if (trimmedContent) {
          if (trimmedContent.startsWith('{') && trimmedContent.endsWith('}')) {
            // JSX 表达式
            contentStr = trimmedContent.slice(1, -1);
          } else {
            // 纯文本
            contentStr = `"${trimmedContent}"`;
          }
        }

        return contentStr ?
          `h('${tagName}', ${propsStr}, ${contentStr})` :
          `h('${tagName}', ${propsStr})`;
      });

      // 转换自闭合标签 <tag prop={value}/>
      code = code.replace(/<([\w-]+)\s+([^>]*?)\/>/g, (match, tagName, attributes) => {
        const props = this.parseJSXAttributes(attributes);

        const propsStr = Object.keys(props).length > 0 ?
          `{ ${Object.entries(props).map(([k, v]) => `"${k}": ${v}`).join(', ')} }` :
          'null';

        return `h('${tagName}', ${propsStr})`;
      });

      // 转换简单的自闭合标签 <tag/>
      code = code.replace(/<([\w-]+)\/>/g, (match, tagName) => {
        return `h('${tagName}')`;
      });

      return code;
    } catch (error) {
      if (this.options.verbose) {
        console.warn(chalk.yellow(`⚠️ JSX 代码转换失败: ${error.message}`));
      }
      return code;
    }
  }

  /**
   * 解析 JSX 属性字符串为对象
   * @param {string} attributesStr - 属性字符串
   * @returns {Object} 属性对象
   */
  parseJSXAttributes(attributesStr) {
    const props = {};

    try {
      // 首先匹配所有 prop={value} 或 prop="value" 格式，支持 kebab-case 属性名
      const attrPattern = /([\w-]+)=\{([^}]+)\}/g;
      let match;

      while ((match = attrPattern.exec(attributesStr)) !== null) {
        const [, name, jsValue] = match;
        props[name] = jsValue;
      }

      // 然后匹配字符串值属性
      const stringAttrPattern = /([\w-]+)=(?:"([^"]+)"|'([^']+)')/g;
      let stringMatch;

      while ((stringMatch = stringAttrPattern.exec(attributesStr)) !== null) {
        const [, name, stringValue1, stringValue2] = stringMatch;
        const value = stringValue1 || stringValue2;
        props[name] = `"${value}"`;
      }

      // 移除已处理的属性，避免重复处理
      let remainingAttrs = attributesStr;
      Object.keys(props).forEach(propName => {
        const propPattern = new RegExp(`\\b${propName.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&')}\\s*=\\s*(?:\\{[^}]+\\}|"[^"]*"|'[^']*')`, 'g');
        remainingAttrs = remainingAttrs.replace(propPattern, '');
      });

      // 最后匹配布尔属性 (单独的属性名)，支持 kebab-case
      const boolAttrPattern = /\b([\w-]+)\b/g;
      let boolMatch;

      while ((boolMatch = boolAttrPattern.exec(remainingAttrs)) !== null) {
        const [, name] = boolMatch;
        // 确保不是已经处理过的属性，也不是常见的非属性词汇
        if (!props[name] && !['class', 'style'].includes(name) && name.length > 1) {
          props[name] = 'true';
        }
      }
    } catch (error) {
      if (this.options.verbose) {
        console.warn(chalk.yellow(`⚠️ 解析 JSX 属性失败: ${error.message}`));
      }
    }

    return props;
  }

  /**
   * 确保代码中包含 h 函数的导入
   * @param {string} code - 源代码
   * @returns {string} 包含 h 导入的代码
   */
  ensureHImport(code) {
    // 如果已经有 h 导入，直接返回
    if (code.includes('import { h }') || code.includes('import {h}')) {
      return code;
    }

    // 查找现有的 vue 导入
    const vueImportMatch = code.match(/import\s+{([^}]+)}\s+from\s+['"]vue['"]/);
    if (vueImportMatch) {
      // 如果已有 vue 导入，添加 h 到导入列表
      const existingImports = vueImportMatch[1].trim();
      const newImports = `h, ${existingImports}`;
      return code.replace(vueImportMatch[0], `import { ${newImports} } from 'vue'`);
    }

    // 如果没有 vue 导入，在文件开头添加
    const scriptMatch = code.match(/(<script[^>]*>)([\s\S]*?)(<\/script>)/);
    if (scriptMatch) {
      const scriptContent = scriptMatch[2];
      const newScriptContent = `\nimport { h } from 'vue'\n${scriptContent}`;
      return code.replace(scriptMatch[0], `${scriptMatch[1]}${newScriptContent}${scriptMatch[3]}`);
    }

    // 如果没有 script 标签，在代码开头添加
    return `import { h } from 'vue'\n${code}`;
  }

  /**
   * 解析内联样式字符串为对象
   * @param {string} styleStr - 样式字符串
   * @returns {Object} 样式对象
   */
  parseInlineStyle(styleStr) {
    const styleObj = {};
    const declarations = styleStr.split(';').filter(d => d.trim());

    declarations.forEach(decl => {
      const [property, value] = decl.split(':').map(s => s.trim());
      if (property && value) {
        // 转换 kebab-case 为 camelCase
        const camelProperty = property.replace(/-([a-z])/g, (match, letter) => letter.toUpperCase());
        styleObj[camelProperty] = value;
      }
    });

    return styleObj;
  }

  /**
   * 获取转换统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    return { ...this.stats };
  }
}

module.exports = RenderFunctionTransformer;
