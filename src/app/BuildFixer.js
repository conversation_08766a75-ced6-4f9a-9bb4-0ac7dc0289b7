const { execSync } = require('child_process');
const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const ora = require('ora');
const BuildFixAgent = require('../domain/build-fix/BuildFixAgent');
const ConfigLoader = require('../domain/build-fix/ConfigLoader');
const BuildExecutor = require('../domain/build-fix/NpmBuildExecutor');

/**
 * AI Agent 构建错误修复器
 * 基于强大的 AI Agent 能力，自动分析和修复构建错误
 *
 * 核心特性：
 * - AI 驱动的错误分析，无需预定义分类
 * - 两阶段修复：文件选择 → 文件修改
 * - 工具调用机制，类似 Augment Agent
 * - 简化的配置和更强的适应性
 */

class BuildFixer {
  constructor(projectPath, options = {}) {
    this.projectPath = projectPath;
    this.userOptions = options;

    // 初始化基本选项，避免测试失败
    this.options = {
      buildCommand: 'npm run build',
      devCommand: 'npm run dev',
      installCommand: 'npm install',
      maxAttempts: 6,
      mode: 'build',
      devTimeout: 30000,
      legacyPeerDeps: true,
      skipInstall: false,
      skipAI: false,
      dryRun: false,
      interactive: false,
      explain: false,
      verbose: false,
      maxRetries: 3,
      // 运行时错误修复选项
      enableRuntimeRepair: false, // 默认禁用，需要明确启用
      runtimeTimeout: 60000, // 运行时监控超时时间（1分钟）
      runtimeAutoFix: true, // 是否自动修复运行时错误
      runtimePort: 3000, // 开发服务器端口
      ...options
    };

    // 配置加载器
    this.configLoader = new ConfigLoader();

    // 构建执行器
    this.buildExecutor = null;

    // 构建统计
    this.buildStats = {
      startTime: null,
      endTime: null,
      duration: 0,
      buildAttempts: 0,
      buildSuccess: false,
      finalBuildSuccess: false,
      errorsFound: [],
      errorsFixed: 0
    };

    this.spinner = null;
    this.agent = null;
  }

  // Spinner 管理方法
  startSpinner(text) {
    if (this.options.verbose) {
      console.log(chalk.gray(`[SPINNER] ${text}`));
      return;
    }

    if (this.spinner) {
      this.spinner.stop();
    }
    this.spinner = ora(text).start();
  }

  updateSpinner(text) {
    if (this.spinner) {
      this.spinner.text = text;
    }
  }

  succeedSpinner(text) {
    if (this.spinner) {
      this.spinner.succeed(text);
      this.spinner = null;
    }
  }

  failSpinner(text) {
    if (this.spinner) {
      this.spinner.fail(text);
      this.spinner = null;
    }
  }

  stopSpinner() {
    if (this.spinner) {
      this.spinner.stop();
      this.spinner = null;
    }
  }

  /**
   * 执行构建并修复错误 - 主入口方法
   */
  async buildAndFix() {
    this.buildStats.startTime = Date.now();

    try {
      this.startSpinner('开始构建项目并修复错误...')
      await this.initialize()

      let buildResult
      if (this.options.mode === 'dev') {
        this.updateSpinner('执行 dev 模式错误检测...')
        buildResult = await this.buildExecutor.performDevCheck()
      } else {
        this.updateSpinner('执行构建...')
        buildResult = await this.buildExecutor.performBuild()
      }

      if (buildResult.success) {
        return {
          success: true,
          attempts: 0,
          errorsFixed: 0,
        }
      }

      this.updateSpinner('AI Agent 正在分析构建错误...')
      const fixResult = await this.analyzeAndFixWithAI(buildResult)
      this.stopSpinner()
      await this.printBuildStats()
      return fixResult
    } catch (error) {
      this.failSpinner('构建修复过程失败')
      console.error(chalk.red('❌ 错误详情:'), error.message)
      throw error
    } finally {
      this.buildStats.endTime = Date.now()
      this.buildStats.duration = this.buildStats.endTime - this.buildStats.startTime
      this.stopSpinner()
    }
  }

  /**
   * 初始化构建修复器
   */
  async initialize() {
    const configFileOptions = await this.configLoader.loadConfig(
      this.userOptions.configPath,
      this.projectPath
    );

    // 合并配置：用户选项 > 配置文件 > 默认值
    this.options = {
      buildCommand: 'npm run build',
      devCommand: 'npm run dev',
      installCommand: 'npm install',
      maxAttempts: 6,
      mode: 'build',
      devTimeout: 30000,
      legacyPeerDeps: true,
      skipInstall: false,
      skipAI: false,
      dryRun: false,
      interactive: false,
      explain: false,
      verbose: false,
      maxRetries: 3,
      ...configFileOptions,
      ...this.userOptions
    };

    // 初始化构建执行器
    this.buildExecutor = new BuildExecutor(this.projectPath, {
      buildCommand: this.options.buildCommand,
      devCommand: this.options.devCommand,
      installCommand: this.options.installCommand,
      devTimeout: this.options.devTimeout,
      legacyPeerDeps: this.options.legacyPeerDeps,
      skipInstall: this.options.skipInstall,
      dryRun: this.options.dryRun,
      verbose: this.options.verbose
    });

    // 初始化 AI Agent
    this.agent = new BuildFixAgent(this.projectPath, {
      maxTokens: this.options.maxTokens || 4000,
      temperature: this.options.temperature || 0.1,
      maxRetries: this.options.maxRetries || 3,
      logDir: this.options.logDir || path.join(this.projectPath, 'ai-logs'),
      maxAttempts: this.options.maxAttempts,
      dryRun: this.options.dryRun,
      verbose: this.options.verbose
    });

    console.log(chalk.blue('🔧 构建修复器配置:'));
    console.log(chalk.gray(`  项目路径: ${this.projectPath}`));
    console.log(chalk.gray(`  构建命令: ${this.options.buildCommand}`));
    console.log(chalk.gray(`  运行模式: ${this.options.mode}`));
    console.log(chalk.gray(`  最大尝试: ${this.options.maxAttempts}`));
    console.log(chalk.gray(`  AI 修复: ${this.options.skipAI ? '禁用' : '启用'}`));
  }

  /**
   * 使用 AI Agent 分析和修复错误
   */
  async analyzeAndFixWithAI(buildResult) {
    if (this.options.skipAI) {
      console.log(chalk.yellow('⚠️  跳过 AI 修复步骤'));
      return this.createResult(false, 'AI 修复被跳过');
    }

    if (!this.agent || !this.agent.isEnabled()) {
      console.log(chalk.yellow('⚠️  AI 服务不可用，无法进行智能修复'));
      return this.createResult(false, 'AI 服务不可用');
    }

    let buildOutput = buildResult.output || '';
    console.log(chalk.blue('\n🤖 AI Agent 开始分析构建错误...'));

    // 尝试修复错误
    for (let attempt = 1; attempt <= this.options.maxAttempts; attempt++) {
      console.log(chalk.blue(`\n🔧 修复尝试 ${attempt}/${this.options.maxAttempts}...`));

      const fixResult = await this.performAIFix(buildOutput, attempt);

      if (fixResult.filesModified > 0) {
        console.log(chalk.green(`✅ AI Agent 修改了 ${fixResult.filesModified} 个文件`));
        this.buildStats.errorsFixed += fixResult.filesModified;

        // 重新构建验证修复效果
        console.log(chalk.blue('\n🔄 重新构建项目验证修复效果...'));
        const newBuildResult = await this.buildExecutor.performBuild();

        if (newBuildResult.success) {
          console.log(chalk.green('🎉 构建成功！所有错误已修复'));
          this.buildStats.finalBuildSuccess = true;
          this.displaySuccessMessage();

          return this.createResult(true);
        } else {
          console.log(chalk.yellow('⚠️  构建仍有问题，准备下一轮修复...'));
          buildOutput = newBuildResult.output;
        }
      } else {
        console.log(chalk.yellow('⚠️  本轮 AI Agent 未能修复任何文件'));
      }
    }

    console.log(chalk.red(`❌ 经过 ${this.options.maxAttempts} 次尝试，仍无法完全修复构建错误`));
    return this.createResult(false, `AI 修复未能完全解决问题，已尝试 ${this.options.maxAttempts} 次`);
  }

  /**
   * 执行 AI 修复 - 委托给 BuildFixAgent
   */
  async performAIFix(buildOutput, attemptNumber = 1) {
    try {
      console.log(chalk.gray('  📋 阶段1: AI 分析错误并选择相关文件...'));
      const analysisResult = await this.agent.errorAnalyzer.analyzeBuildErrors(buildOutput, attemptNumber);

      if (!analysisResult.success) {
        console.log(chalk.yellow('  ⚠️  AI 错误分析失败'));
        return { filesModified: 0, error: analysisResult.error };
      }

      this.agent.recordAttempt(buildOutput, analysisResult.filesToFix);

      // 使用处理后的输出和建议信息
      const processedOutput = analysisResult.processedOutput || buildOutput;
      const suggestions = analysisResult.suggestions || [];

      console.log(chalk.gray('  🔧 阶段2: AI 修复相关文件...'));
      const fixResult = await this.agent.fixFiles(
        analysisResult.filesToFix,
        processedOutput,
        attemptNumber,
        suggestions
      );

      return fixResult;
    } catch (error) {
      console.error(chalk.red('  ❌ AI 修复过程异常:'), error.message);
      return { filesModified: 0, error: error.message };
    }
  }

  async removeProblematicConfig() {
    const vueConfigPath = path.join(this.projectPath, 'vue.config.js');

    try {
      if (await fs.pathExists(vueConfigPath)) {
        const content = await fs.readFile(vueConfigPath, 'utf8');

        // 删除有问题的 preload 插件配置
        const fixedContent = content.replace(
          /config\.plugin\('preload'\)\.use\(\{\s*\/\/[^}]*\}\)/g,
          '// Removed problematic preload plugin configuration'
        );

        if (fixedContent !== content) {
          await this.writeFileWithBackup(vueConfigPath, fixedContent);
          console.log(chalk.green('    ✅ 已删除有问题的插件配置'));
          return { filesModified: 1 };
        }
      }
    } catch (error) {
      console.log(chalk.yellow(`    ⚠️  替代策略失败: ${error.message}`));
    }

    return { filesModified: 0 };
  }

  displaySuccessMessage() {
    console.log(chalk.green('\n🎉 恭喜！所有构建错误已修复'));
    console.log(chalk.blue('\n💡 后续建议:'));
    console.log(chalk.gray('  1. 运行测试确保功能正常'));
    console.log(chalk.gray('  2. 检查修复后的代码是否符合预期'));
    console.log(chalk.gray('  3. 提交代码变更'));
  }

  createResult(success, reason = null, remainingErrors = 0) {
    return {
      success,
      attempts: this.buildStats.buildAttempts,
      errorsFixed: this.buildStats.errorsFixed,
      remainingErrors,
      duration: this.buildStats.duration,
      reason
    };
  }

  async printBuildStats() {
    console.log('\n' + chalk.bold('🏗️  构建修复统计:'));
    console.log(`构建尝试: ${this.buildExecutor ? this.buildExecutor.getBuildAttempts() : this.buildStats.buildAttempts} 次`);
    console.log(`发现错误: ${this.buildStats.errorsFound.length} 个`);
    console.log(`修复错误: ${this.buildStats.errorsFixed} 个`);
    console.log(`最终状态: ${this.buildStats.finalBuildSuccess ? chalk.green('成功') : chalk.red('失败')}`);

    // 显示 AI Agent 统计信息
    if (this.agent) {
      const agentStats = this.agent.getFixStats();
      console.log(`AI 分析文件: ${agentStats.filesAnalyzed} 个`);
      console.log(`AI 修改文件: ${agentStats.filesModified} 个`);
      console.log(`AI 尝试次数: ${agentStats.attempts} 次`);

      // 列出所有轮次的日志文件
      await this.agent.listSessionLogs();

      // 生成会话摘要
      try {
        const summaryPath = await this.agent.generateSessionSummary();
        if (summaryPath) {
          console.log(chalk.blue(`📊 详细日志摘要: ${path.relative(this.projectPath, summaryPath)}`));
        }
      } catch (error) {
        console.warn(chalk.yellow(`⚠️  生成会话摘要失败: ${error.message}`));
      }

      // 保存修复历史摘要
      try {
        const historyPath = await this.agent.saveSessionHistory();
        if (historyPath) {
          console.log(chalk.blue(`📝 修复历史摘要: ${path.relative(this.projectPath, historyPath)}`));
        }
      } catch (error) {
        console.warn(chalk.yellow(`⚠️  保存修复历史摘要失败: ${error.message}`));
      }

      // 显示历史统计信息
      if (agentStats.historyStats) {
        const histStats = agentStats.historyStats.statistics;
        console.log(`修复成功率: ${Math.round(histStats.successRate * 100)}%`);
        console.log(`处理文件数: ${histStats.totalFiles} 个`);

        if (agentStats.historyStats.recommendations.length > 0) {
          console.log(chalk.yellow('\n💡 建议:'));
          agentStats.historyStats.recommendations.forEach(rec => {
            console.log(chalk.yellow(`  - ${rec.message}`));
          });
        }
      }
    }
  }

  // 为了保持向后兼容性，添加一些测试需要的方法
  detectErrorStart(errorMessage) {
    // 简单的错误类型检测
    if (errorMessage.includes('.ts(') || errorMessage.includes('error TS')) {
      return {
        type: 'typescript',
        file: errorMessage.match(/([^(]+)\(/)?.[1] || 'unknown'
      };
    }
    return { type: 'unknown', file: 'unknown' };
  }

  categorizeErrors(errors) {
    return errors.map(error => {
      if (error.category) return error;
      if (/Cannot find module|Module not found/i.test(error.message)) {
        return { ...error, category: 'missing-module' };
      }
      if (/Property \'\w+\' does not exist on type/.test(error.message)) {
        return { ...error, category: 'property-not-exist' };
      }
      if (/Vue version mismatch|Vue packages version mismatch|Vue 2 syntax is not supported/i.test(error.message)) {
        return { ...error, category: 'vue-version' };
      }
      if (error.type === 'vue') {
        return { ...error, category: 'vue' };
      }
      if (error.type === 'typescript') {
        return { ...error, category: 'typescript' };
      }
      return { ...error, category: 'unknown' };
    });
  }

  generateBuildErrorPrompt(content, error) {
    return `构建错误修复: ${error.message}`;
  }

  validateRepairedContent(repairedContent, originalContent) {
    return repairedContent !== originalContent && repairedContent.length > 0;
  }

  getBuildStats() {
    return {
      ...this.buildStats,
      buildAttempts: this.buildExecutor ? this.buildExecutor.getBuildAttempts() : this.buildStats.buildAttempts
    };
  }

  // 添加缺失的方法
  parseErrors(errorOutput) {
    const errors = [];
    const lines = errorOutput.split('\n');

    for (const line of lines) {
      const tsMatch = line.match(/([^(]+)\((\d+),(\d+)\):\s*error\s+TS\d+:(.+)/);
      if (tsMatch) {
        errors.push({
          type: 'typescript',
          file: tsMatch[1],
          line: parseInt(tsMatch[2]),
          column: parseInt(tsMatch[3]),
          message: tsMatch[4].trim(),
          category: 'typescript'
        });

        continue;
      }

      // Vue 编译错误（如：src/App.vue:25:15: Template compilation error）
      const vueColonMatch = line.match(/([\w\/-]+\.vue):(\d+):(\d+):\s*(.+)/i);
      if (vueColonMatch) {
        errors.push({
          type: 'vue',
          file: vueColonMatch[1],
          line: parseInt(vueColonMatch[2]),
          column: parseInt(vueColonMatch[3]),
          message: vueColonMatch[4].trim(),
          category: 'vue'
        });
        continue;
      }

      // property-not-exist 错误
      if (/Property \'\w+\' does not exist on type/.test(line)) {
        errors.push({
          type: 'typescript',
          message: line.trim(),
          category: 'property-not-exist'
        });
        continue;
      }

      // vue-version 错误
      if (/Vue version mismatch|Vue packages version mismatch|Vue 2 syntax is not supported/i.test(line)) {
        errors.push({
          type: 'vue',
          message: line.trim(),
          category: 'vue-version'
        });
        continue;
      }

      // Webpack 错误
      if (line.includes('ERROR in') || line.includes('Module not found')) {
        errors.push({
          type: 'webpack',
          message: line.trim(),
          category: 'webpack'
        });
        continue;
      }

      // missing-module 错误
      if (/Cannot find module|Module not found/i.test(line)) {
        errors.push({
          type: 'webpack',
          message: line.trim(),
          category: 'missing-module'
        });
        continue;
      }
    }

    return errors;
  }

  async writeFileWithBackup(filePath, content) {
    if (this.options.dryRun) {
      console.log(chalk.gray(`[预览模式] 将写入文件: ${filePath}`));
      return;
    }

    // 备份原文件
    if (await fs.pathExists(filePath)) {
      const backupPath = `${filePath}.backup.${Date.now()}`;
      await fs.copy(filePath, backupPath);
    }

    await fs.writeFile(filePath, content, 'utf8');
  }
}

module.exports = BuildFixer;
