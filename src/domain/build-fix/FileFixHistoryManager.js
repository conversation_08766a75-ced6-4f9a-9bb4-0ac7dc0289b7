const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const crypto = require('crypto');

/**
 * 文件修复历史管理器
 * 负责管理每个文件的修复历史，包括失败原因、修复策略、AI响应等
 * 用于在后续修复尝试中提供历史信息，避免重复相同的错误
 */
class FileFixHistoryManager {
  constructor(projectPath, options = {}) {
    this.projectPath = projectPath;
    this.options = {
      maxHistoryPerFile: 5, // 每个文件最多保留5次修复历史
      historyDir: path.join(projectPath, 'ai-logs', 'fix-history'),
      enablePersistence: true, // 是否持久化到文件
      verbose: false,
      ...options
    };

    // 内存中的修复历史
    this.fileHistories = new Map(); // filePath -> FixHistory[]
    
    // 会话级别的修复历史
    this.sessionHistory = {
      sessionId: this._generateSessionId(),
      startTime: new Date().toISOString(),
      globalAttempts: 0,
      patterns: new Map(), // 错误模式 -> 修复策略
      successfulStrategies: new Map(), // 成功的修复策略
      failedStrategies: new Map() // 失败的修复策略
    };

    this._ensureHistoryDir();
  }

  /**
   * 生成会话ID
   * @private
   */
  _generateSessionId() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0];
    const randomId = Math.random().toString(36).substring(2, 8);
    return `fix-session-${timestamp}-${randomId}`;
  }

  /**
   * 确保历史目录存在
   * @private
   */
  async _ensureHistoryDir() {
    if (this.options.enablePersistence) {
      try {
        await fs.ensureDir(this.options.historyDir);
      } catch (error) {
        console.warn(chalk.yellow(`⚠️  无法创建历史目录: ${error.message}`));
        this.options.enablePersistence = false;
      }
    }
  }

  /**
   * 开始文件修复尝试
   * @param {string} filePath - 文件路径
   * @param {string} buildOutput - 构建输出
   * @param {number} attemptNumber - 尝试次数
   * @param {Object} context - 上下文信息
   * @returns {Object} 修复上下文，包含历史信息
   */
  async startFileFixAttempt(filePath, buildOutput, attemptNumber, context = {}) {
    const normalizedPath = path.relative(this.projectPath, filePath);
    
    // 获取文件的修复历史
    const fileHistory = await this.getFileHistory(normalizedPath);
    
    // 创建新的修复尝试记录
    const fixAttempt = {
      attemptId: this._generateAttemptId(normalizedPath, attemptNumber),
      filePath: normalizedPath,
      attemptNumber,
      globalAttemptNumber: ++this.sessionHistory.globalAttempts,
      startTime: new Date().toISOString(),
      endTime: null,
      buildOutput,
      buildOutputHash: this._hashContent(buildOutput),
      context,
      strategy: null,
      aiCalls: [],
      result: null,
      success: false,
      error: null,
      duration: 0,
      metadata: {
        fileSize: 0,
        originalContent: null,
        finalContent: null,
        contentHash: null
      }
    };

    // 读取文件信息
    try {
      if (await fs.pathExists(filePath)) {
        const content = await fs.readFile(filePath, 'utf8');
        fixAttempt.metadata.fileSize = content.length;
        fixAttempt.metadata.originalContent = content;
        fixAttempt.metadata.contentHash = this._hashContent(content);
      }
    } catch (error) {
      fixAttempt.error = `无法读取文件: ${error.message}`;
    }

    // 添加到文件历史
    if (!this.fileHistories.has(normalizedPath)) {
      this.fileHistories.set(normalizedPath, []);
    }
    this.fileHistories.get(normalizedPath).push(fixAttempt);

    if (this.options.verbose) {
      console.log(chalk.gray(`📝 开始文件修复尝试: ${normalizedPath} (第${attemptNumber}次)`));
    }

    return {
      attemptId: fixAttempt.attemptId,
      fileHistory: fileHistory.slice(), // 返回历史副本
      previousFailures: this._extractPreviousFailures(fileHistory),
      suggestedStrategies: this._suggestStrategies(normalizedPath, buildOutput, fileHistory),
      patterns: this._identifyErrorPatterns(buildOutput, fileHistory)
    };
  }

  /**
   * 记录AI调用
   * @param {string} attemptId - 尝试ID
   * @param {Object} aiCallData - AI调用数据
   */
  recordAICall(attemptId, aiCallData) {
    const attempt = this._findAttemptById(attemptId);
    if (!attempt) {
      console.warn(chalk.yellow(`⚠️  找不到修复尝试: ${attemptId}`));
      return;
    }

    const aiCall = {
      callId: this._generateCallId(),
      timestamp: new Date().toISOString(),
      taskType: aiCallData.taskType || 'unknown',
      phase: aiCallData.phase || 'unknown',
      prompt: aiCallData.prompt || '',
      response: aiCallData.response || '',
      success: aiCallData.success || false,
      error: aiCallData.error || null,
      duration: aiCallData.duration || 0,
      tokens: aiCallData.tokens || { input: 0, output: 0 },
      strategy: aiCallData.strategy || null
    };

    attempt.aiCalls.push(aiCall);
    
    if (aiCall.strategy) {
      attempt.strategy = aiCall.strategy;
    }

    if (this.options.verbose) {
      console.log(chalk.gray(`🤖 记录AI调用: ${aiCall.taskType}/${aiCall.phase} (${aiCall.success ? '成功' : '失败'})`));
    }
  }

  /**
   * 结束文件修复尝试
   * @param {string} attemptId - 尝试ID
   * @param {boolean} success - 是否成功
   * @param {Object} result - 修复结果
   * @param {string} error - 错误信息
   */
  async endFileFixAttempt(attemptId, success, result = null, error = null) {
    const attempt = this._findAttemptById(attemptId);
    if (!attempt) {
      console.warn(chalk.yellow(`⚠️  找不到修复尝试: ${attemptId}`));
      return;
    }

    const endTime = new Date();
    attempt.endTime = endTime.toISOString();
    attempt.duration = endTime.getTime() - new Date(attempt.startTime).getTime();
    attempt.success = success;
    attempt.result = result;
    attempt.error = error;

    // 如果修复成功，读取最终文件内容
    if (success && result && result.newContent) {
      attempt.metadata.finalContent = result.newContent;
      attempt.metadata.contentHash = this._hashContent(result.newContent);
    }

    // 更新策略统计
    if (attempt.strategy) {
      const strategyMap = success ? this.sessionHistory.successfulStrategies : this.sessionHistory.failedStrategies;
      const count = strategyMap.get(attempt.strategy) || 0;
      strategyMap.set(attempt.strategy, count + 1);
    }

    // 识别和记录错误模式
    if (!success && error) {
      this._recordErrorPattern(attempt.buildOutput, attempt.strategy, error);
    }

    // 持久化历史记录
    if (this.options.enablePersistence) {
      await this._persistFileHistory(attempt.filePath);
    }

    const status = success ? '✅ 成功' : '❌ 失败';
    if (this.options.verbose) {
      console.log(chalk.gray(`📝 结束文件修复尝试: ${attempt.filePath} ${status} (${attempt.duration}ms)`));
    }

    return attempt;
  }

  /**
   * 获取文件的修复历史
   * @param {string} filePath - 文件路径
   * @returns {Array} 修复历史数组
   */
  async getFileHistory(filePath) {
    const normalizedPath = path.relative(this.projectPath, filePath);
    
    // 先从内存获取
    let history = this.fileHistories.get(normalizedPath) || [];
    
    // 如果启用持久化且内存中没有，尝试从文件加载
    if (history.length === 0 && this.options.enablePersistence) {
      history = await this._loadFileHistory(normalizedPath);
      if (history.length > 0) {
        this.fileHistories.set(normalizedPath, history);
      }
    }

    return history;
  }

  /**
   * 获取文件的上一次失败信息
   * @param {string} filePath - 文件路径
   * @returns {Object|null} 上一次失败的详细信息
   */
  async getLastFailure(filePath) {
    const history = await this.getFileHistory(filePath);
    const failures = history.filter(attempt => !attempt.success);
    
    if (failures.length === 0) {
      return null;
    }

    const lastFailure = failures[failures.length - 1];
    return {
      attemptNumber: lastFailure.attemptNumber,
      error: lastFailure.error,
      strategy: lastFailure.strategy,
      buildOutput: lastFailure.buildOutput,
      aiCalls: lastFailure.aiCalls,
      duration: lastFailure.duration,
      timestamp: lastFailure.endTime,
      patterns: this._extractErrorPatterns(lastFailure.buildOutput)
    };
  }

  /**
   * 获取推荐的修复策略
   * @param {string} filePath - 文件路径
   * @param {string} buildOutput - 构建输出
   * @returns {Array} 推荐策略列表
   */
  async getRecommendedStrategies(filePath, buildOutput) {
    const history = await this.getFileHistory(filePath);
    return this._suggestStrategies(filePath, buildOutput, history);
  }

  /**
   * 生成修复尝试ID
   * @private
   */
  _generateAttemptId(filePath, attemptNumber) {
    const hash = crypto.createHash('md5').update(`${filePath}-${attemptNumber}-${Date.now()}`).digest('hex');
    return `attempt-${hash.substring(0, 8)}`;
  }

  /**
   * 生成AI调用ID
   * @private
   */
  _generateCallId() {
    return `call-${Date.now()}-${Math.random().toString(36).substring(2, 6)}`;
  }

  /**
   * 计算内容哈希
   * @private
   */
  _hashContent(content) {
    return crypto.createHash('md5').update(content || '').digest('hex');
  }

  /**
   * 根据尝试ID查找修复尝试
   * @private
   */
  _findAttemptById(attemptId) {
    for (const [filePath, history] of this.fileHistories) {
      const attempt = history.find(a => a.attemptId === attemptId);
      if (attempt) {
        return attempt;
      }
    }
    return null;
  }

  /**
   * 提取之前的失败信息
   * @private
   */
  _extractPreviousFailures(history) {
    return history
      .filter(attempt => !attempt.success && attempt.endTime)
      .map(attempt => ({
        attemptNumber: attempt.attemptNumber,
        error: attempt.error,
        strategy: attempt.strategy,
        buildOutputHash: attempt.buildOutputHash,
        duration: attempt.duration,
        aiCallsCount: attempt.aiCalls.length,
        patterns: this._extractErrorPatterns(attempt.buildOutput)
      }))
      .slice(-3); // 只返回最近3次失败
  }

  /**
   * 建议修复策略
   * @private
   */
  _suggestStrategies(filePath, buildOutput, history) {
    const strategies = [];
    const errorPatterns = this._extractErrorPatterns(buildOutput);

    // 基于历史成功策略
    const successfulStrategies = Array.from(this.sessionHistory.successfulStrategies.entries())
      .sort((a, b) => b[1] - a[1]) // 按成功次数排序
      .slice(0, 3)
      .map(([strategy, count]) => ({
        type: 'historical-success',
        strategy,
        confidence: Math.min(count / 5, 1), // 最高置信度为1
        reason: `该策略在本次会话中成功${count}次`
      }));

    strategies.push(...successfulStrategies);

    // 基于错误模式匹配
    for (const pattern of errorPatterns) {
      if (this.sessionHistory.patterns.has(pattern)) {
        const patternStrategy = this.sessionHistory.patterns.get(pattern);
        strategies.push({
          type: 'pattern-match',
          strategy: patternStrategy,
          confidence: 0.8,
          reason: `匹配到已知错误模式: ${pattern}`
        });
      }
    }

    // 基于文件类型的默认策略
    const fileExt = path.extname(filePath);
    const defaultStrategies = this._getDefaultStrategies(fileExt, errorPatterns);
    strategies.push(...defaultStrategies);

    // 避免重复失败的策略
    const failedStrategies = new Set(
      history
        .filter(attempt => !attempt.success)
        .map(attempt => attempt.strategy)
        .filter(Boolean)
    );

    return strategies
      .filter(s => !failedStrategies.has(s.strategy))
      .sort((a, b) => b.confidence - a.confidence)
      .slice(0, 5);
  }

  /**
   * 识别错误模式
   * @private
   */
  _identifyErrorPatterns(buildOutput, history) {
    const patterns = this._extractErrorPatterns(buildOutput);
    const result = {
      current: patterns,
      recurring: [],
      new: []
    };

    // 找出重复出现的模式
    const historicalPatterns = new Set();
    history.forEach(attempt => {
      const attemptPatterns = this._extractErrorPatterns(attempt.buildOutput);
      attemptPatterns.forEach(pattern => historicalPatterns.add(pattern));
    });

    patterns.forEach(pattern => {
      if (historicalPatterns.has(pattern)) {
        result.recurring.push(pattern);
      } else {
        result.new.push(pattern);
      }
    });

    return result;
  }

  /**
   * 提取错误模式
   * @private
   */
  _extractErrorPatterns(buildOutput) {
    const patterns = [];

    if (!buildOutput) return patterns;

    // Vue相关错误模式
    const vuePatterns = [
      /Cannot resolve dependency: (.+)/,
      /Module not found: Error: Can't resolve '(.+)'/,
      /export '(.+)' \(imported as '(.+)'\) was not found/,
      /Syntax error: (.+)/,
      /TypeError: (.+)/,
      /ReferenceError: (.+)/,
      /Vue warn.*: (.+)/,
      /Failed to compile with (\d+) error/
    ];

    vuePatterns.forEach(pattern => {
      const matches = buildOutput.match(pattern);
      if (matches) {
        patterns.push(matches[0].substring(0, 100)); // 限制长度
      }
    });

    return patterns;
  }

  /**
   * 记录错误模式
   * @private
   */
  _recordErrorPattern(buildOutput, strategy, error) {
    const patterns = this._extractErrorPatterns(buildOutput);
    patterns.forEach(pattern => {
      if (!this.sessionHistory.patterns.has(pattern)) {
        this.sessionHistory.patterns.set(pattern, strategy);
      }
    });
  }

  /**
   * 获取默认策略
   * @private
   */
  _getDefaultStrategies(fileExt, errorPatterns) {
    const strategies = [];

    if (fileExt === '.vue') {
      strategies.push({
        type: 'default',
        strategy: 'vue-component-fix',
        confidence: 0.6,
        reason: 'Vue组件文件的标准修复策略'
      });
    }

    if (fileExt === '.js' || fileExt === '.ts') {
      strategies.push({
        type: 'default',
        strategy: 'javascript-syntax-fix',
        confidence: 0.5,
        reason: 'JavaScript文件的标准修复策略'
      });
    }

    // 基于错误模式的策略
    const hasImportError = errorPatterns.some(p => p.includes('Cannot resolve') || p.includes('Module not found'));
    if (hasImportError) {
      strategies.push({
        type: 'error-based',
        strategy: 'import-resolution-fix',
        confidence: 0.7,
        reason: '检测到导入解析错误'
      });
    }

    return strategies;
  }

  /**
   * 持久化文件历史
   * @private
   */
  async _persistFileHistory(filePath) {
    if (!this.options.enablePersistence) return;

    try {
      const history = this.fileHistories.get(filePath) || [];
      const historyFile = this._getHistoryFilePath(filePath);

      // 只保留最近的历史记录
      const trimmedHistory = history.slice(-this.options.maxHistoryPerFile);

      await fs.writeJson(historyFile, {
        filePath,
        lastUpdated: new Date().toISOString(),
        history: trimmedHistory
      }, { spaces: 2 });

      if (this.options.verbose) {
        console.log(chalk.gray(`💾 已保存文件历史: ${filePath}`));
      }
    } catch (error) {
      console.warn(chalk.yellow(`⚠️  保存文件历史失败: ${error.message}`));
    }
  }

  /**
   * 加载文件历史
   * @private
   */
  async _loadFileHistory(filePath) {
    if (!this.options.enablePersistence) return [];

    try {
      const historyFile = this._getHistoryFilePath(filePath);

      if (await fs.pathExists(historyFile)) {
        const data = await fs.readJson(historyFile);
        return data.history || [];
      }
    } catch (error) {
      if (this.options.verbose) {
        console.warn(chalk.yellow(`⚠️  加载文件历史失败: ${error.message}`));
      }
    }

    return [];
  }

  /**
   * 获取历史文件路径
   * @private
   */
  _getHistoryFilePath(filePath) {
    const safePath = filePath.replace(/[/\\]/g, '_').replace(/[^a-zA-Z0-9._-]/g, '');
    return path.join(this.options.historyDir, `${safePath}.json`);
  }

  /**
   * 清理过期的历史记录
   */
  async cleanupOldHistory(daysToKeep = 7) {
    if (!this.options.enablePersistence) return;

    try {
      const files = await fs.readdir(this.options.historyDir);
      const cutoffTime = Date.now() - (daysToKeep * 24 * 60 * 60 * 1000);

      for (const file of files) {
        if (!file.endsWith('.json')) continue;

        const filePath = path.join(this.options.historyDir, file);
        const stats = await fs.stat(filePath);

        if (stats.mtime.getTime() < cutoffTime) {
          await fs.remove(filePath);
          if (this.options.verbose) {
            console.log(chalk.gray(`🗑️  已删除过期历史文件: ${file}`));
          }
        }
      }
    } catch (error) {
      console.warn(chalk.yellow(`⚠️  清理历史记录失败: ${error.message}`));
    }
  }

  /**
   * 生成会话摘要
   */
  generateSessionSummary() {
    const totalAttempts = Array.from(this.fileHistories.values())
      .reduce((sum, history) => sum + history.length, 0);

    const successfulAttempts = Array.from(this.fileHistories.values())
      .reduce((sum, history) => sum + history.filter(a => a.success).length, 0);

    const totalFiles = this.fileHistories.size;

    return {
      sessionId: this.sessionHistory.sessionId,
      startTime: this.sessionHistory.startTime,
      endTime: new Date().toISOString(),
      statistics: {
        totalFiles,
        totalAttempts,
        successfulAttempts,
        successRate: totalAttempts > 0 ? (successfulAttempts / totalAttempts) : 0,
        globalAttempts: this.sessionHistory.globalAttempts
      },
      strategies: {
        successful: Object.fromEntries(this.sessionHistory.successfulStrategies),
        failed: Object.fromEntries(this.sessionHistory.failedStrategies),
        patterns: Object.fromEntries(this.sessionHistory.patterns)
      },
      files: Array.from(this.fileHistories.keys()),
      recommendations: this._generateRecommendations()
    };
  }

  /**
   * 生成建议
   * @private
   */
  _generateRecommendations() {
    const recommendations = [];

    // 基于成功率的建议
    const summary = this.generateSessionSummary();
    if (summary.statistics.successRate < 0.5) {
      recommendations.push({
        type: 'low-success-rate',
        message: '修复成功率较低，建议检查项目配置或寻求人工干预',
        priority: 'high'
      });
    }

    // 基于重复失败的建议
    const failedStrategies = this.sessionHistory.failedStrategies;
    for (const [strategy, count] of failedStrategies) {
      if (count >= 3) {
        recommendations.push({
          type: 'repeated-failure',
          message: `策略 "${strategy}" 多次失败，建议尝试其他方法`,
          priority: 'medium'
        });
      }
    }

    return recommendations;
  }

  /**
   * 保存会话摘要
   */
  async saveSessionSummary() {
    if (!this.options.enablePersistence) return null;

    try {
      const summary = this.generateSessionSummary();
      const summaryFile = path.join(this.options.historyDir, `${this.sessionHistory.sessionId}-summary.json`);

      await fs.writeJson(summaryFile, summary, { spaces: 2 });

      if (this.options.verbose) {
        console.log(chalk.blue(`📊 会话摘要已保存: ${path.basename(summaryFile)}`));
      }

      return summaryFile;
    } catch (error) {
      console.warn(chalk.yellow(`⚠️  保存会话摘要失败: ${error.message}`));
      return null;
    }
  }

  /**
   * 提取之前的失败信息
   * @private
   */
  _extractPreviousFailures(history) {
    return history
      .filter(attempt => !attempt.success && attempt.endTime)
      .map(attempt => ({
        attemptNumber: attempt.attemptNumber,
        error: attempt.error,
        strategy: attempt.strategy,
        buildOutputHash: attempt.buildOutputHash,
        duration: attempt.duration,
        aiCallsCount: attempt.aiCalls.length,
        patterns: this._extractErrorPatterns(attempt.buildOutput)
      }))
      .slice(-3); // 只返回最近3次失败
  }

  /**
   * 建议修复策略
   * @private
   */
  _suggestStrategies(filePath, buildOutput, history) {
    const strategies = [];
    const errorPatterns = this._extractErrorPatterns(buildOutput);

    // 基于历史成功策略
    const successfulStrategies = Array.from(this.sessionHistory.successfulStrategies.entries())
      .sort((a, b) => b[1] - a[1]) // 按成功次数排序
      .slice(0, 3)
      .map(([strategy, count]) => ({
        type: 'historical-success',
        strategy,
        confidence: Math.min(count / 5, 1), // 最高置信度为1
        reason: `该策略在本次会话中成功${count}次`
      }));

    strategies.push(...successfulStrategies);

    // 基于错误模式匹配
    for (const pattern of errorPatterns) {
      if (this.sessionHistory.patterns.has(pattern)) {
        const patternStrategy = this.sessionHistory.patterns.get(pattern);
        strategies.push({
          type: 'pattern-match',
          strategy: patternStrategy,
          confidence: 0.8,
          reason: `匹配到已知错误模式: ${pattern}`
        });
      }
    }

    // 基于文件类型的默认策略
    const fileExt = path.extname(filePath);
    const defaultStrategies = this._getDefaultStrategies(fileExt, errorPatterns);
    strategies.push(...defaultStrategies);

    // 避免重复失败的策略
    const failedStrategies = new Set(
      history
        .filter(attempt => !attempt.success)
        .map(attempt => attempt.strategy)
        .filter(Boolean)
    );

    return strategies
      .filter(s => !failedStrategies.has(s.strategy))
      .sort((a, b) => b.confidence - a.confidence)
      .slice(0, 5);
  }

  /**
   * 识别错误模式
   * @private
   */
  _identifyErrorPatterns(buildOutput, history) {
    const patterns = this._extractErrorPatterns(buildOutput);
    const result = {
      current: patterns,
      recurring: [],
      new: []
    };

    // 找出重复出现的模式
    const historicalPatterns = new Set();
    history.forEach(attempt => {
      const attemptPatterns = this._extractErrorPatterns(attempt.buildOutput);
      attemptPatterns.forEach(pattern => historicalPatterns.add(pattern));
    });

    patterns.forEach(pattern => {
      if (historicalPatterns.has(pattern)) {
        result.recurring.push(pattern);
      } else {
        result.new.push(pattern);
      }
    });

    return result;
  }

  /**
   * 提取错误模式
   * @private
   */
  _extractErrorPatterns(buildOutput) {
    const patterns = [];

    if (!buildOutput) return patterns;

    // Vue相关错误模式
    const vuePatterns = [
      /Cannot resolve dependency: (.+)/,
      /Module not found: Error: Can't resolve '(.+)'/,
      /export '(.+)' \(imported as '(.+)'\) was not found/,
      /Syntax error: (.+)/,
      /TypeError: (.+)/,
      /ReferenceError: (.+)/,
      /Vue warn.*: (.+)/,
      /Failed to compile with (\d+) error/
    ];

    vuePatterns.forEach(pattern => {
      const matches = buildOutput.match(pattern);
      if (matches) {
        patterns.push(matches[0].substring(0, 100)); // 限制长度
      }
    });

    return patterns;
  }

  /**
   * 记录错误模式
   * @private
   */
  _recordErrorPattern(buildOutput, strategy, error) {
    const patterns = this._extractErrorPatterns(buildOutput);
    patterns.forEach(pattern => {
      if (!this.sessionHistory.patterns.has(pattern)) {
        this.sessionHistory.patterns.set(pattern, strategy);
      }
    });
  }

  /**
   * 获取默认策略
   * @private
   */
  _getDefaultStrategies(fileExt, errorPatterns) {
    const strategies = [];

    if (fileExt === '.vue') {
      strategies.push({
        type: 'default',
        strategy: 'vue-component-fix',
        confidence: 0.6,
        reason: 'Vue组件文件的标准修复策略'
      });
    }

    if (fileExt === '.js' || fileExt === '.ts') {
      strategies.push({
        type: 'default',
        strategy: 'javascript-syntax-fix',
        confidence: 0.5,
        reason: 'JavaScript文件的标准修复策略'
      });
    }

    // 基于错误模式的策略
    const hasImportError = errorPatterns.some(p => p.includes('Cannot resolve') || p.includes('Module not found'));
    if (hasImportError) {
      strategies.push({
        type: 'error-based',
        strategy: 'import-resolution-fix',
        confidence: 0.7,
        reason: '检测到导入解析错误'
      });
    }

    return strategies;
  }

  /**
   * 持久化文件历史
   * @private
   */
  async _persistFileHistory(filePath) {
    if (!this.options.enablePersistence) return;

    try {
      const history = this.fileHistories.get(filePath) || [];
      const historyFile = this._getHistoryFilePath(filePath);

      // 只保留最近的历史记录
      const trimmedHistory = history.slice(-this.options.maxHistoryPerFile);

      await fs.writeJson(historyFile, {
        filePath,
        lastUpdated: new Date().toISOString(),
        history: trimmedHistory
      }, { spaces: 2 });

      if (this.options.verbose) {
        console.log(chalk.gray(`💾 已保存文件历史: ${filePath}`));
      }
    } catch (error) {
      console.warn(chalk.yellow(`⚠️  保存文件历史失败: ${error.message}`));
    }
  }

  /**
   * 加载文件历史
   * @private
   */
  async _loadFileHistory(filePath) {
    if (!this.options.enablePersistence) return [];

    try {
      const historyFile = this._getHistoryFilePath(filePath);

      if (await fs.pathExists(historyFile)) {
        const data = await fs.readJson(historyFile);
        return data.history || [];
      }
    } catch (error) {
      if (this.options.verbose) {
        console.warn(chalk.yellow(`⚠️  加载文件历史失败: ${error.message}`));
      }
    }

    return [];
  }

  /**
   * 获取历史文件路径
   * @private
   */
  _getHistoryFilePath(filePath) {
    const safePath = filePath.replace(/[/\\]/g, '_').replace(/[^a-zA-Z0-9._-]/g, '');
    return path.join(this.options.historyDir, `${safePath}.json`);
  }

  /**
   * 清理过期的历史记录
   */
  async cleanupOldHistory(daysToKeep = 7) {
    if (!this.options.enablePersistence) return;

    try {
      const files = await fs.readdir(this.options.historyDir);
      const cutoffTime = Date.now() - (daysToKeep * 24 * 60 * 60 * 1000);

      for (const file of files) {
        if (!file.endsWith('.json')) continue;

        const filePath = path.join(this.options.historyDir, file);
        const stats = await fs.stat(filePath);

        if (stats.mtime.getTime() < cutoffTime) {
          await fs.remove(filePath);
          if (this.options.verbose) {
            console.log(chalk.gray(`🗑️  已删除过期历史文件: ${file}`));
          }
        }
      }
    } catch (error) {
      console.warn(chalk.yellow(`⚠️  清理历史记录失败: ${error.message}`));
    }
  }

  /**
   * 生成会话摘要
   */
  generateSessionSummary() {
    const totalAttempts = Array.from(this.fileHistories.values())
      .reduce((sum, history) => sum + history.length, 0);

    const successfulAttempts = Array.from(this.fileHistories.values())
      .reduce((sum, history) => sum + history.filter(a => a.success).length, 0);

    const totalFiles = this.fileHistories.size;

    return {
      sessionId: this.sessionHistory.sessionId,
      startTime: this.sessionHistory.startTime,
      endTime: new Date().toISOString(),
      statistics: {
        totalFiles,
        totalAttempts,
        successfulAttempts,
        successRate: totalAttempts > 0 ? (successfulAttempts / totalAttempts) : 0,
        globalAttempts: this.sessionHistory.globalAttempts
      },
      strategies: {
        successful: Object.fromEntries(this.sessionHistory.successfulStrategies),
        failed: Object.fromEntries(this.sessionHistory.failedStrategies),
        patterns: Object.fromEntries(this.sessionHistory.patterns)
      },
      files: Array.from(this.fileHistories.keys()),
      recommendations: this._generateRecommendations()
    };
  }

  /**
   * 生成建议
   * @private
   */
  _generateRecommendations() {
    const recommendations = [];

    // 计算成功率
    const totalAttempts = Array.from(this.fileHistories.values())
      .reduce((sum, history) => sum + history.length, 0);
    const successfulAttempts = Array.from(this.fileHistories.values())
      .reduce((sum, history) => sum + history.filter(a => a.success).length, 0);
    const successRate = totalAttempts > 0 ? (successfulAttempts / totalAttempts) : 0;

    // 基于成功率的建议
    if (successRate < 0.5) {
      recommendations.push({
        type: 'low-success-rate',
        message: '修复成功率较低，建议检查项目配置或寻求人工干预',
        priority: 'high'
      });
    }

    // 基于重复失败的建议
    const failedStrategies = this.sessionHistory.failedStrategies;
    for (const [strategy, count] of failedStrategies) {
      if (count >= 3) {
        recommendations.push({
          type: 'repeated-failure',
          message: `策略 "${strategy}" 多次失败，建议尝试其他方法`,
          priority: 'medium'
        });
      }
    }

    return recommendations;
  }

  /**
   * 保存会话摘要
   */
  async saveSessionSummary() {
    if (!this.options.enablePersistence) return null;

    try {
      const summary = this.generateSessionSummary();
      const summaryFile = path.join(this.options.historyDir, `${this.sessionHistory.sessionId}-summary.json`);

      await fs.writeJson(summaryFile, summary, { spaces: 2 });

      if (this.options.verbose) {
        console.log(chalk.blue(`📊 会话摘要已保存: ${path.basename(summaryFile)}`));
      }

      return summaryFile;
    } catch (error) {
      console.warn(chalk.yellow(`⚠️  保存会话摘要失败: ${error.message}`));
      return null;
    }
  }
}

module.exports = FileFixHistoryManager;
