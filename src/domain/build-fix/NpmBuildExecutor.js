const { execSync, spawn } = require('child_process');
const chalk = require('chalk');
const BuildErrorAnalyzer = require('./BuildErrorAnalyzer');

/**
 * 构建执行器
 * 负责项目的构建和依赖安装
 */
class NpmBuildExecutor {
  constructor(projectPath, options = {}) {
    this.projectPath = projectPath;
    this.options = {
      buildCommand: 'pnpm run build',
      devCommand: 'pnpm run dev',
      installCommand: 'pnpm install',
      devTimeout: 30000,
      legacyPeerDeps: true,
      skipInstall: false,
      dryRun: false,
      verbose: false,
      ...options
    };

    this.buildAttempts = 0;

    // 初始化错误分析器
    this.errorAnalyzer = new BuildErrorAnalyzer(projectPath, null, null, {
      verbose: this.options.verbose
    });
  }

  /**
   * 执行构建过程（包含依赖安装和构建）
   */
  async performBuild() {
    try {
      // 安装依赖
      await this.installDependencies();

      // 执行构建
      return await this.executeBuild();
    } catch (error) {
      return {
        success: false,
        output: error.stdout + error.stderr,
        error: error.message
      };
    }
  }

  /**
   * 执行 Dev 模式检测（30秒运行）
   */
  async performDevCheck() {
    try {
      // 安装依赖
      await this.installDependencies();

      // 执行 dev 命令并在 30 秒后停止
      return await this.executeDevCommand();
    } catch (error) {
      return {
        success: false,
        output: error.stdout + error.stderr,
        error: error.message
      };
    }
  }

  /**
   * 安装项目依赖
   */
  async installDependencies() {
    if (this.options.skipInstall) {
      console.log(chalk.gray('⚠️  跳过依赖安装'));
      return;
    }

    if (this.options.dryRun) {
      console.log(chalk.gray('🔍 [DRY RUN] 跳过依赖安装'));
      return;
    }

    console.log(chalk.blue('📦 正在安装依赖...'));

    try {
      const installOutput = execSync(this.options.installCommand, {
        cwd: this.projectPath,
        encoding: 'utf8',
        stdio: 'pipe'
      });

      if (this.options.verbose) {
        console.log(chalk.green('✅ 依赖安装完成'));
        console.log(chalk.gray('安装输出:'));
        console.log(chalk.gray(installOutput));
      }
    } catch (error) {
      if (this.options.legacyPeerDeps && this.shouldUseLegacyPeerDeps(error)) {
        await this.installWithLegacyPeerDeps();
      } else {
        console.log(chalk.yellow('⚠️ 依赖安装可能不完整，继续尝试构建'));
        this.logInstallError(error);
      }
    }
  }

  /**
   * 检查是否应该使用 legacy peer deps
   */
  shouldUseLegacyPeerDeps(error) {
    const errorOutput = error.stdout + error.stderr;
    return errorOutput.includes('ERESOLVE unable to resolve dependency tree') ||
           errorOutput.includes('Fix the upstream dependency conflict') ||
           errorOutput.includes('--legacy-peer-deps') ||
           errorOutput.includes('peer dependency') ||
           errorOutput.includes('Could not resolve dependency');
  }

  /**
   * 使用 legacy peer deps 安装依赖
   */
  async installWithLegacyPeerDeps() {
    console.log(chalk.blue('🔄 检测到依赖冲突，尝试使用 --legacy-peer-deps 重新安装...'));

    try {
      const legacyCommand = this.options.installCommand + ' --legacy-peer-deps';
      const legacyInstallOutput = execSync(legacyCommand, {
        cwd: this.projectPath,
        encoding: 'utf8',
        stdio: 'pipe'
      });

      if (this.options.verbose) {
        console.log(chalk.green('✅ 使用 --legacy-peer-deps 安装依赖成功'));
        console.log(chalk.gray('安装输出:'));
        console.log(chalk.gray(legacyInstallOutput));
      }
    } catch (legacyError) {
      console.log(chalk.red('❌ 即使使用 --legacy-peer-deps 也无法安装依赖'));
      this.logInstallError(legacyError);
    }
  }

  /**
   * 执行构建命令
   */
  async executeBuild() {
    if (this.options.dryRun) {
      console.log(chalk.gray('🔍 [DRY RUN] 跳过构建执行'));
      return { success: true, output: 'DRY RUN - Build skipped' };
    }

    console.log(chalk.blue(`🔨 执行构建命令: ${this.options.buildCommand}`));
    this.buildAttempts++;

    try {
      const output = execSync(this.options.buildCommand, {
        cwd: this.projectPath,
        encoding: 'utf8',
        stdio: 'pipe'
      });

      console.log(chalk.green('✅ 构建成功'));
      return {
        success: true,
        output
      };
    } catch (error) {
      console.log(chalk.red('❌ 构建失败'));
      return this.errorAnalyzer.handleBuildError(error, this.options);
    }
  }

  /**
   * 执行 Dev 命令（30秒超时）
   */
  async executeDevCommand() {
    if (this.options.dryRun) {
      console.log(chalk.gray('🔍 [DRY RUN] 跳过 dev 命令执行'));
      return { success: true, output: 'DRY RUN - Dev command skipped' };
    }

    const devCommand = this.options.devCommand || 'pnpm dev';
    const timeout = this.options.devTimeout || 30000; // 30秒

    console.log(chalk.blue(`🚀 执行 dev 命令: ${devCommand} (${timeout / 1000}s 超时)`));
    this.buildAttempts++;

    return new Promise((resolve) => {
      let output = '';
      let hasErrors = false;

      const child = spawn('sh', ['-c', devCommand], {
        cwd: this.projectPath,
        stdio: 'pipe'
      });

      // 收集输出
      child.stdout.on('data', (data) => {
        const text = data.toString();
        output += text;

        // 检查是否有错误
        if (this.errorAnalyzer.containsErrors(text)) {
          hasErrors = true;
        }

        if (this.options.verbose) {
          process.stdout.write(chalk.gray(text));
        }
      });

      child.stderr.on('data', (data) => {
        const text = data.toString();
        output += text;
        hasErrors = true;

        if (this.options.verbose) {
          process.stderr.write(chalk.red(text));
        }
      });

      // 30秒后停止进程
      const timer = setTimeout(() => {
        child.kill('SIGTERM');

        // 如果进程没有优雅退出，强制杀死
        setTimeout(() => {
          if (!child.killed) {
            child.kill('SIGKILL');
          }
        }, 5000);

        if (hasErrors) {
          console.log(chalk.red('❌ Dev 模式检测到错误'));
          resolve(this.errorAnalyzer.handleBuildError({ stdout: output, stderr: '' }, this.options));
        } else {
          console.log(chalk.green('✅ Dev 模式运行正常'));
          resolve({ success: true, output });
        }
      }, timeout);

      child.on('exit', (code) => {
        clearTimeout(timer);

        if (code !== 0 || hasErrors) {
          console.log(chalk.red('❌ Dev 命令执行失败'));
          resolve(this.errorAnalyzer.handleBuildError({ stdout: output, stderr: '' }, this.options));
        } else {
          console.log(chalk.green('✅ Dev 命令执行成功'));
          resolve({ success: true, output });
        }
      });
    });
  }

  /**
   * 启动开发服务器（非阻塞）
   * 用于运行时错误监控
   */
  async startDevServer(options = {}) {
    if (this.options.dryRun) {
      console.log(chalk.gray('🔍 [DRY RUN] 跳过开发服务器启动'));
      return { mock: true };
    }

    const devCommand = this.options.devCommand || 'npm run dev';
    const port = options.port || 3000;

    console.log(chalk.blue(`🌐 启动开发服务器: ${devCommand}`));

    return new Promise((resolve, reject) => {
      const child = spawn('sh', ['-c', devCommand], {
        cwd: this.projectPath,
        stdio: 'pipe',
        env: {
          ...process.env,
          PORT: port.toString()
        }
      });

      let output = '';
      let serverStarted = false;

      // 监听输出，检测服务器是否启动成功
      child.stdout.on('data', (data) => {
        const text = data.toString();
        output += text;

        if (this.options.verbose) {
          process.stdout.write(chalk.gray(text));
        }

        // 检测服务器启动成功的标志
        if (!serverStarted && this.isServerReady(text)) {
          serverStarted = true;
          console.log(chalk.green(`✅ 开发服务器启动成功 (PID: ${child.pid})`));
          resolve(child);
        }
      });

      child.stderr.on('data', (data) => {
        const text = data.toString();
        output += text;

        if (this.options.verbose) {
          process.stderr.write(chalk.red(text));
        }

        // 检查是否有致命错误
        if (this.isFatalError(text)) {
          reject(new Error(`开发服务器启动失败: ${text}`));
        }
      });

      child.on('exit', (code) => {
        if (!serverStarted) {
          reject(new Error(`开发服务器进程退出 (code: ${code})`));
        }
      });

      // 30秒超时 - 给Vue项目更多启动时间
      setTimeout(() => {
        if (!serverStarted) {
          child.kill('SIGTERM');
          reject(new Error('开发服务器启动超时'));
        }
      }, 30000);
    });
  }

  /**
   * 检测服务器是否准备就绪
   */
  isServerReady(text) {
    const readyPatterns = [
      /Local:\s+http/i,
      /ready in/i,
      /compiled successfully/i,
      /webpack compiled/i,
      /dev server running/i,
      /server running/i,
      /listening on/i,
      /App running at/i,
      /Network:\s+http/i,
      /development server/i,
      /serving at/i,
      /available on/i,
      /started successfully/i
    ];

    return readyPatterns.some(pattern => pattern.test(text));
  }

  /**
   * 检测是否为致命错误
   */
  isFatalError(text) {
    const fatalPatterns = [
      /EADDRINUSE/i,
      /port.*already in use/i,
      /cannot find module/i,
      /module not found/i,
      /failed to compile/i
    ];

    return fatalPatterns.some(pattern => pattern.test(text));
  }

  /**
   * 获取构建尝试次数
   */
  getBuildAttempts() {
    return this.buildAttempts;
  }

  /**
   * 重置构建尝试次数
   */
  resetBuildAttempts() {
    this.buildAttempts = 0;
  }

  /**
   * 记录安装错误
   */
  logInstallError(error) {
    if (this.options.verbose) {
      console.log(chalk.red('安装错误详情:'));
      console.log(chalk.red(error.stdout || ''));
      console.log(chalk.red(error.stderr || ''));
    }
  }
}

module.exports = NpmBuildExecutor;
