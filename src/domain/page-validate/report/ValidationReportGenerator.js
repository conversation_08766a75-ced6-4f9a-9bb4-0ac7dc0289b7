const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');

/**
 * ValidationReportGenerator - 验证报告生成器
 *
 * 职责：
 * 1. 生成各种格式的验证报告
 * 2. 统计验证结果数据
 * 3. 格式化错误信息
 * 4. 生成可视化报告
 */
class ValidationReportGenerator {
  constructor(projectPath, options = {}) {
    this.projectPath = projectPath;
    this.options = {
      outputDir: 'validation-reports',
      verbose: false,
      includeScreenshots: true,
      includeNavigationHistory: true,
      includeFAQAnalysis: true,
      ...options
    };

    this.outputDir = path.join(this.projectPath, this.options.outputDir);
  }

  /**
   * 生成完整报告
   */
  generateReport(validationResults, faqAnalysis = null) {
    const totalPages = validationResults.length;
    const successfulPages = validationResults.filter(r => r.success).length;
    const failedPages = validationResults.filter(r => !r.success);

    // 收集所有错误
    const allErrors = [];
    const routeErrors = [];

    // 收集导航统计信息
    const navigationStats = this.generateNavigationStats(validationResults);

    for (const result of validationResults) {
      if (result.errors && result.errors.length > 0) {
        allErrors.push(...result.errors);
      }

      // 收集路由错误
      if (result.loginConsoleErrors && result.loginConsoleErrors.length > 0) {
        routeErrors.push(...result.loginConsoleErrors);
        allErrors.push(...result.loginConsoleErrors);
      }
    }

    const report = {
      summary: {
        total: totalPages,
        successful: successfulPages,
        failed: failedPages.length,
        successRate: totalPages > 0 ? (successfulPages / totalPages * 100).toFixed(2) : 0,
        routeErrors: routeErrors.length,
        navigation: navigationStats
      },
      results: validationResults,
      failedPages: failedPages,
      routeErrors: routeErrors,
      faqAnalysis: faqAnalysis,
      timestamp: new Date().toISOString()
    };

    return report;
  }

  /**
   * 生成导航统计信息
   */
  generateNavigationStats(validationResults) {
    let totalNavigations = 0;
    let pagesWithRedirects = 0;
    let loginRedirects = 0;
    let errorPageRedirects = 0;
    let httpRedirects = 0;

    for (const result of validationResults) {
      if (result.navigationHistory && result.navigationHistory.length > 0) {
        const navigations = result.navigationHistory.length - 1; // 减去初始页面
        if (navigations > 0) {
          totalNavigations += navigations;
          pagesWithRedirects++;

          // 统计不同类型的跳转
          for (const nav of result.navigationHistory) {
            if (nav.isLoginRedirect) {
              loginRedirects++;
            }
            if (nav.isErrorPage) {
              errorPageRedirects++;
            }
            if (nav.type === 'redirect') {
              httpRedirects++;
            }
          }
        }
      }
    }

    return {
      totalNavigations,
      pagesWithRedirects,
      loginRedirects,
      errorPageRedirects,
      httpRedirects
    };
  }

  /**
   * 打印控制台摘要
   */
  printSummary(report) {
    console.log(chalk.blue('\n📊 验证结果摘要:'));
    console.log(chalk.gray(`   总页面数: ${report.summary.total}`));
    console.log(chalk.green(`   成功: ${report.summary.successful}`));
    console.log(chalk.red(`   失败: ${report.summary.failed}`));
    console.log(chalk.blue(`   成功率: ${report.summary.successRate}%`));

    // 显示路由错误统计
    if (report.summary.routeErrors > 0) {
      console.log(chalk.yellow(`   路由错误: ${report.summary.routeErrors} 个`));
    }

    // 显示导航统计
    this.printNavigationSummary(report.summary.navigation);

    // 显示失败页面详情
    if (report.failedPages.length > 0) {
      this.printFailedPages(report);
    }

    // 显示FAQ分析结果
    if (report.faqAnalysis && this.options.includeFAQAnalysis) {
      this.printFAQAnalysis(report.faqAnalysis);
    }

    // 显示路由错误详情
    if (report.routeErrors && report.routeErrors.length > 0) {
      this.printRouteErrors(report.routeErrors);
    }
  }

  /**
   * 打印导航统计摘要
   */
  printNavigationSummary(nav) {
    if (nav.totalNavigations > 0) {
      console.log(chalk.cyan(`\n🔄 页面跳转统计:`));
      console.log(chalk.gray(`   总跳转次数: ${nav.totalNavigations}`));
      console.log(chalk.gray(`   有跳转的页面: ${nav.pagesWithRedirects}`));

      if (nav.loginRedirects > 0) {
        console.log(chalk.yellow(`   登录重定向: ${nav.loginRedirects} 次`));
      }

      if (nav.httpRedirects > 0) {
        console.log(chalk.magenta(`   HTTP重定向: ${nav.httpRedirects} 次`));
      }

      if (nav.errorPageRedirects > 0) {
        console.log(chalk.red(`   错误页面跳转: ${nav.errorPageRedirects} 次`));
      }
    } else {
      console.log(chalk.green(`\n✅ 所有页面均无跳转，直接加载成功`));
    }
  }

  /**
   * 打印失败页面详情
   */
  printFailedPages(report) {
    console.log(chalk.red('\n❌ 失败的页面:'));
    for (const failed of report.failedPages) {
      console.log(chalk.red(`   ${failed.route.path}: ${failed.errors.length} 个错误`));

      // 显示错误的详细信息
      if (failed.errors.length > 0) {
        const formattedErrors = this.formatErrorsForDisplay(failed.errors);
        formattedErrors.forEach((errorInfo, index) => {
          console.log(chalk.red(`     ${index + 1}. ${errorInfo.type}: ${errorInfo.summary}`));
          if (errorInfo.details && this.options.verbose) {
            console.log(chalk.gray(`        详情: ${errorInfo.details}`));
          }
        });

        if (failed.errors.length > formattedErrors.length) {
          console.log(chalk.gray(`     ... 还有 ${failed.errors.length - formattedErrors.length} 个类似错误`));
        }
      }
    }
  }

  /**
   * 打印FAQ分析结果
   */
  printFAQAnalysis(faqAnalysis) {
    if (faqAnalysis && faqAnalysis.suggestions.length > 0) {
      console.log(chalk.blue('\n💡 常见问题解决建议:'));
      console.log(chalk.gray(faqAnalysis.summary));

      // 显示前3个最相关的建议
      const topSuggestions = faqAnalysis.suggestions.slice(0, 3);
      for (const suggestion of topSuggestions) {
        const count = faqAnalysis.errorCounts.get(suggestion.solution) || 0;
        console.log(chalk.yellow(`   • ${suggestion.solution} (${count} 个相关错误)`));

        if (suggestion.summary && suggestion.summary.cause) {
          console.log(chalk.gray(`     原因: ${suggestion.summary.cause}`));
        }
      }

      console.log(chalk.gray('\n   📖 详细解决方案请参考: docs/runtime-faq-summary.md'));
    }
  }

  /**
   * 打印路由错误详情
   */
  printRouteErrors(routeErrors) {
    console.log(chalk.yellow('\n🚨 路由错误详情:'));
    for (const error of routeErrors) {
      console.log(chalk.yellow(`   - [${error.phase}] ${error.message.substring(0, 150)}${error.message.length > 150 ? '...' : ''}`));
      if (this.options.verbose && error.timestamp) {
        console.log(chalk.gray(`     时间: ${error.timestamp}`));
      }
    }
    console.log(chalk.gray('\n   💡 提示: 路由错误通常需要修复路由配置文件'));
  }

  /**
   * 格式化错误信息用于显示
   */
  formatErrorsForDisplay(errors, maxErrors = 5) {
    const formatted = [];
    const errorGroups = new Map();

    // 按错误类型分组
    for (const error of errors) {
      const key = error.type || 'unknown';
      if (!errorGroups.has(key)) {
        errorGroups.set(key, []);
      }
      errorGroups.get(key).push(error);
    }

    // 格式化每组错误
    let count = 0;
    for (const [type, groupErrors] of errorGroups) {
      if (count >= maxErrors) break;

      const firstError = groupErrors[0];
      const summary = this.summarizeError(firstError);

      formatted.push({
        type: type,
        summary: summary,
        details: groupErrors.length > 1 ? `${groupErrors.length} 个类似错误` : firstError.stack,
        count: groupErrors.length
      });

      count++;
    }

    return formatted;
  }

  /**
   * 总结错误信息
   */
  summarizeError(error) {
    if (typeof error === 'string') {
      return error.substring(0, 100);
    }

    if (error.message) {
      return error.message.substring(0, 100);
    }

    return JSON.stringify(error).substring(0, 100);
  }

  /**
   * 保存Markdown报告
   */
  async saveMarkdownReport(report,  outputPath = 'validation-report.md') {
    await fs.ensureDir(this.outputDir);
    const markdown = this.generateMarkdownReport(report);

    await fs.writeFile(outputPath, markdown, 'utf8');
    console.log(chalk.green(`📄 验证报告已保存: ${outputPath}`));

    return outputPath;
  }

  /**
   * 生成Markdown格式的报告
   */
  generateMarkdownReport(report) {
    let markdown = `# 页面验证报告\n\n`;
    markdown += `生成时间: ${report.timestamp}\n\n`;

    // 摘要部分
    markdown += `## 摘要\n\n`;
    markdown += `- 总页面数: ${report.summary.total}\n`;
    markdown += `- 成功: ${report.summary.successful}\n`;
    markdown += `- 失败: ${report.summary.failed}\n`;
    markdown += `- 成功率: ${report.summary.successRate}%\n`;

    // 导航统计
    if (this.options.includeNavigationHistory) {
      markdown += this.generateNavigationMarkdown(report.summary.navigation);
    }

    // 失败页面详情
    if (report.failedPages.length > 0) {
      markdown += this.generateFailedPagesMarkdown(report.failedPages);
    }

    // FAQ分析结果
    if (report.faqAnalysis && this.options.includeFAQAnalysis) {
      markdown += this.generateFAQMarkdown(report.faqAnalysis);
    }

    // 详细结果
    markdown += `## 详细结果\n\n`;
    for (const result of report.results) {
      const status = result.success ? '✅' : '❌';
      markdown += `- ${status} ${result.route.path} (${result.loadTime}ms)\n`;
    }

    return markdown;
  }

  /**
   * 生成导航部分的Markdown
   */
  generateNavigationMarkdown(nav) {
    let markdown = '';

    if (nav.totalNavigations > 0) {
      markdown += `\n### 🔄 页面跳转统计\n\n`;
      markdown += `- 总跳转次数: ${nav.totalNavigations}\n`;
      markdown += `- 有跳转的页面: ${nav.pagesWithRedirects}\n`;

      if (nav.loginRedirects > 0) {
        markdown += `- 登录重定向: ${nav.loginRedirects} 次\n`;
      }

      if (nav.httpRedirects > 0) {
        markdown += `- HTTP重定向: ${nav.httpRedirects} 次\n`;
      }

      if (nav.errorPageRedirects > 0) {
        markdown += `- 错误页面跳转: ${nav.errorPageRedirects} 次\n`;
      }
    } else {
      markdown += `\n### ✅ 页面跳转\n\n`;
      markdown += `所有页面均无跳转，直接加载成功\n`;
    }

    return markdown;
  }

  /**
   * 生成失败页面部分的Markdown
   */
  generateFailedPagesMarkdown(failedPages) {
    let markdown = `\n## 失败的页面\n\n`;

    for (const failed of failedPages) {
      markdown += `### ${failed.route.path}\n\n`;
      markdown += `- URL: ${failed.url}\n`;
      markdown += `- 加载时间: ${failed.loadTime}ms\n`;

      if (failed.errors.length > 0) {
        markdown += `- 错误:\n`;
        const formattedErrors = this.formatErrorsForDisplay(failed.errors);
        for (const errorInfo of formattedErrors) {
          markdown += `  - **${errorInfo.type}**: ${errorInfo.summary}\n`;
          if (errorInfo.details) {
            markdown += `    \`\`\`\n    ${errorInfo.details}\n    \`\`\`\n`;
          }
        }

        if (failed.errors.length > formattedErrors.length) {
          markdown += `  - ... 还有 ${failed.errors.length - formattedErrors.length} 个类似错误\n`;
        }
      }

      if (failed.warnings.length > 0) {
        markdown += `- 警告:\n`;
        for (const warning of failed.warnings) {
          markdown += `  - ${warning}\n`;
        }
      }

      markdown += `\n`;
    }

    return markdown;
  }

  /**
   * 生成FAQ部分的Markdown
   */
  generateFAQMarkdown(faqAnalysis) {
    let markdown = `\n## 💡 常见问题解决建议\n\n`;
    markdown += `${faqAnalysis.summary}\n\n`;

    for (const suggestion of faqAnalysis.suggestions) {
      const count = faqAnalysis.errorCounts.get(suggestion.solution) || 0;
      markdown += `### ${suggestion.solution} (${count} 个相关错误)\n\n`;

      if (suggestion.summary) {
        if (suggestion.summary.cause) {
          markdown += `**根本原因**: ${suggestion.summary.cause}\n\n`;
        }

        if (suggestion.summary.steps.length > 0) {
          markdown += `**解决步骤**:\n`;
          for (const step of suggestion.summary.steps) {
            markdown += `1. ${step}\n`;
          }
          markdown += `\n`;
        }
      }
    }

    markdown += `> 📖 详细解决方案请参考: [docs/runtime-faq-summary.md](docs/runtime-faq-summary.md)\n\n`;
    return markdown;
  }

  /**
   * 保存JSON报告
   */
  async saveJSONReport(report, filename = 'validation-report.json') {
    await fs.ensureDir(this.outputDir);
    const outputPath = path.join(this.outputDir, filename);

    await fs.writeFile(outputPath, JSON.stringify(report, null, 2), 'utf8');
    console.log(chalk.green(`📄 JSON报告已保存: ${outputPath}`));

    return outputPath;
  }
}

module.exports = ValidationReportGenerator;
