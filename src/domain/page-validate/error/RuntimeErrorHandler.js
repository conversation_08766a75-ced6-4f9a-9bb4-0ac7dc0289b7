const chalk = require('chalk');
const express = require('express');
const crypto = require('crypto');
const { v4: uuidv4 } = require('uuid');
const BuildFixAgent = require('../../build-fix/BuildFixAgent');
const BuildErrorAnalyzer = require('../../build-fix/BuildErrorAnalyzer');

/**
 * RuntimeErrorHandler - Vue运行时错误处理器
 *
 * 功能：
 * 1. 在webpack dev server中添加错误接收路由
 * 2. 注入Vue错误处理器代码
 * 3. 收集运行时错误信息
 * 4. 通过AI分析并修复运行时错误
 * 5. 支持热重载和实时修复
 */
class RuntimeErrorHandler {
  constructor(projectPath, options = {}) {
    this.projectPath = projectPath;
    this.options = {
      port: 3000,
      errorEndpoint: '/__runtime_errors__',
      maxErrors: 100,
      autoFix: true,
      verbose: false,
      ...options
    };

    // 错误收集
    this.errorQueue = [];
    this.errorHistory = new Map(); // 错误去重
    this.fixAttempts = new Map(); // 修复尝试记录
    this.pendingFixes = new Set(); // 正在进行的修复

    // AI修复代理
    this.buildFixAgent = new BuildFixAgent(projectPath, {
      maxAttempts: 3,
      verbose: this.options.verbose,
      ...options
    });

    // 构建错误分析器（用于统一的错误输出处理）
    this.buildErrorAnalyzer = new BuildErrorAnalyzer(projectPath, null, null, {
      verbose: this.options.verbose
    });

    // Express路由器
    this.router = express.Router();
    this.setupRoutes();
  }

  /**
   * 设置Express路由
   */
  setupRoutes() {
    // 接收运行时错误的POST路由
    this.router.post(this.options.errorEndpoint, express.json(), (req, res) => {
      this.handleRuntimeError(req.body);
      res.json({ success: true, message: 'Error received' });
    });

    // 获取错误历史的GET路由
    this.router.get(this.options.errorEndpoint, (req, res) => {
      res.json({
        errors: Array.from(this.errorHistory.values()),
        queueLength: this.errorQueue.length
      });
    });

    // 清除错误历史
    this.router.delete(this.options.errorEndpoint, (req, res) => {
      this.clearErrorHistory();
      res.json({ success: true, message: 'Error history cleared' });
    });
  }

  /**
   * 处理接收到的运行时错误
   */
  async handleRuntimeError(errorData) {
    try {
      const errorId = this.generateErrorId(errorData);

      // 检查是否为重复错误
      if (this.errorHistory.has(errorId)) {
        const existingError = this.errorHistory.get(errorId);
        existingError.count++;
        existingError.lastOccurred = new Date();
        return;
      }

      // 创建新的错误记录
      const errorRecord = {
        id: errorId,
        ...errorData,
        count: 1,
        firstOccurred: new Date(),
        lastOccurred: new Date(),
        fixed: false,
        fixAttempts: 0
      };

      this.errorHistory.set(errorId, errorRecord);
      this.errorQueue.push(errorRecord);

      if (this.options.verbose) {
        console.log(chalk.yellow(`🚨 运行时错误: ${errorData.message}`));
        console.log(chalk.gray(`   文件: ${errorData.fileName}:${errorData.lineNumber}`));
      }

      // 自动修复
      if (this.options.autoFix && !this.fixAttempts.has(errorId)) {
        await this.attemptAutoFix(errorRecord);
      }

    } catch (error) {
      console.error(chalk.red(`处理运行时错误失败: ${error.message}`));
    }
  }

  /**
   * 尝试自动修复错误
   */
  async attemptAutoFix(errorRecord) {
    const { id, fileName, message, stack, componentTrace } = errorRecord;

    try {
      this.fixAttempts.set(id, Date.now());
      this.pendingFixes.add(id); // 添加到正在进行的修复列表
      errorRecord.fixAttempts++;

      if (this.options.verbose) {
        console.log(chalk.blue(`🔧 尝试修复运行时错误: ${message}`));
        console.log(chalk.gray(`🔧 开始修复运行时错误: ${fileName}`));
      }

      // 构建错误上下文信息
      const errorContext = this.buildErrorContext(errorRecord);

      // 使用BuildFixAgent进行修复
      const fixResult = await this.buildFixAgent.fixRuntimeError(errorContext);

      if (this.options.verbose) {
        console.log(chalk.gray(`       🔍 修复结果详情: success=${fixResult.success}, error=${fixResult.error || 'none'}`));
      }

      if (fixResult.success) {
        // 确保在 errorHistory 中也更新状态
        errorRecord.fixed = true;
        errorRecord.fixedAt = new Date();

        // 同时更新 errorHistory 中的记录
        if (this.errorHistory.has(id)) {
          const historyRecord = this.errorHistory.get(id);
          historyRecord.fixed = true;
          historyRecord.fixedAt = new Date();
        }

        console.log(chalk.green(`✅ 运行时错误已修复: ${message}`));

        if (this.options.verbose) {
          console.log(chalk.gray(`       ✅ 错误记录已更新: fixed=true`));
        }
      } else {
        console.log(chalk.yellow(`⚠️  运行时错误修复失败: ${fixResult.error}`));
      }

      return fixResult;

    } catch (error) {
      console.error(chalk.red(`自动修复异常: ${error.message}`));
      return { success: false, error: error.message };
    } finally {
      // 无论成功还是失败，都从正在进行的修复列表中移除
      this.pendingFixes.delete(id);
    }
  }

  /**
   * 构建错误上下文信息
   */
  buildErrorContext(errorRecord) {
    const { fileName, lineNumber, columnNumber, message, stack, componentTrace } = errorRecord;

    // 智能推断实际出错的文件
    const actualFileName = this.inferActualErrorFile(errorRecord);

    return {
      type: 'runtime',
      fileName: actualFileName,
      originalFileName: fileName, // 保留原始文件名
      lineNumber,
      columnNumber,
      message,
      stack,
      componentTrace,
      timestamp: errorRecord.firstOccurred,
      count: errorRecord.count,
      // 构建类似构建错误的输出格式
      buildOutput: this.buildErrorAnalyzer.formatRuntimeErrorAsBuildOutput({
        fileName: errorRecord.fileName,
        lineNumber: errorRecord.lineNumber,
        message: errorRecord.message,
        stack: errorRecord.stack,
        componentTrace: errorRecord.componentTrace,
        timestamp: errorRecord.firstOccurred,
        count: errorRecord.count
      }),
      // 添加错误分类信息
      errorCategory: this.categorizeError(errorRecord)
    };
  }

  /**
   * 智能推断实际出错的文件
   */
  inferActualErrorFile(errorRecord) {
    const { fileName, stack, componentTrace, message } = errorRecord;

    // 如果有组件追踪信息，优先使用第一个有效的组件文件
    if (componentTrace && componentTrace.length > 0) {
      for (const trace of componentTrace) {
        if (trace.file && trace.file !== 'unknown' && !trace.file.includes('node_modules')) {
          return trace.file;
        }
      }
    }

    // 从堆栈信息中提取实际的用户代码文件
    if (stack) {
      const stackLines = stack.split('\n');
      for (const line of stackLines) {
        // 查找不在 node_modules 中的文件
        const match = line.match(/\((.*?):(\d+):(\d+)\)/) || line.match(/at (.*?):(\d+):(\d+)/);
        if (match) {
          const filePath = match[1];
          if (filePath && !filePath.includes('node_modules') && !filePath.includes('webpack-internal')) {
            return filePath;
          }
        }
      }
    }

    // Vue Router 特殊处理
    if (message.includes('vue-router') || (stack && stack.includes('vue-router'))) {
      // 尝试找到路由配置文件
      const routerFiles = ['src/router/index.js', 'src/router/index.ts', 'router/index.js', 'router.js'];
      return routerFiles[0]; // 默认返回最常见的路由文件路径
    }

    // 如果都没找到，返回原始文件名
    return fileName || 'unknown';
  }

  /**
   * 错误分类
   */
  categorizeError(errorRecord) {
    const { message, stack } = errorRecord;
    const fullError = `${message} ${stack || ''}`.toLowerCase();

    if (fullError.includes('vue-router') || fullError.includes('router')) {
      return 'vue-router';
    }
    if (fullError.includes('vuex') || fullError.includes('store')) {
      return 'vuex';
    }
    if (fullError.includes('computed') || fullError.includes('reactive')) {
      return 'vue-reactivity';
    }
    if (fullError.includes('component') || fullError.includes('mount')) {
      return 'vue-component';
    }
    if (fullError.includes('template') || fullError.includes('render')) {
      return 'vue-template';
    }

    return 'general';
  }

  /**
   * 生成错误ID用于去重
   */
  generateErrorId(errorData) {
    const { fileName, lineNumber, message } = errorData;
    const key = `${fileName}:${lineNumber}:${message}`;
    return crypto.createHash('md5').update(key).digest('hex').substring(0, 16);
  }

  /**
   * 清除错误历史
   */
  clearErrorHistory() {
    this.errorQueue = [];
    this.errorHistory.clear();
    this.fixAttempts.clear();
    this.pendingFixes.clear();
    console.log(chalk.green('✅ 错误历史已清除'));
  }
}

module.exports = RuntimeErrorHandler;
