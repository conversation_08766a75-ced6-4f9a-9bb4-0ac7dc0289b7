const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');

/**
 * FAQHelper - FAQ 提示系统
 *
 * 功能：
 * 1. 解析 runtime-faq-summary.md 文件
 * 2. 根据错误信息匹配相关的 FAQ 条目
 * 3. 提供解决方案建议
 * 4. 生成错误修复提示
 */
class FAQHelper {
  constructor(projectPath, options = {}) {
    this.projectPath = projectPath;
    this.options = {
      faqPath: path.join(__dirname, '../../../../docs/runtime-faq-summary.md'),
      verbose: false,
      ...options
    };

    this.faqData = null;
    this.errorPatterns = new Map();
    this.solutionMap = new Map();
  }

  /**
   * 初始化 FAQ 数据
   */
  async initialize() {
    try {
      if (!await fs.pathExists(this.options.faqPath)) {
        console.log(chalk.yellow('⚠️  FAQ 文件不存在，跳过 FAQ 提示功能'));
        return false;
      }

      const faqContent = await fs.readFile(this.options.faqPath, 'utf8');
      this.faqData = this.parseFAQContent(faqContent);
      this.buildErrorPatterns();

      if (this.options.verbose) {
        console.log(chalk.gray(`✅ FAQ 系统已初始化，加载了 ${this.errorPatterns.size} 个错误模式`));
      }

      return true;
    } catch (error) {
      console.log(chalk.yellow(`⚠️  FAQ 初始化失败: ${error.message}`));
      return false;
    }
  }

  /**
   * 解析 FAQ 内容
   */
  parseFAQContent(content) {
    const faqData = {
      errorIndex: new Map(),
      solutions: new Map(),
      categories: new Map()
    };

    // 解析错误信息快速索引表
    const indexMatch = content.match(/\| 错误信息关键词 \| 跳转到解决方案 \|([\s\S]*?)\n\n/);
    if (indexMatch) {
      const indexContent = indexMatch[1];
      const lines = indexContent.split('\n').filter(line => line.trim() && line.includes('|'));

      for (const line of lines) {
        const parts = line.split('|').map(p => p.trim()).filter(p => p);
        if (parts.length >= 2) {
          const errorKeyword = parts[0].replace(/`/g, '');
          const solution = parts[1].replace(/\[→ (.*?)\].*/, '$1');
          faqData.errorIndex.set(errorKeyword.toLowerCase(), solution);
        }
      }
    }

    // 解析具体的解决方案
    this.parseSolutions(content, faqData);

    return faqData;
  }

  /**
   * 解析解决方案内容
   */
  parseSolutions(content, faqData) {
    // 匹配各个解决方案章节
    const solutionSections = [
      { name: '异步组件导入错误', pattern: /### 异步组件导入错误([\s\S]*?)(?=###|---|\n## )/g },
      { name: 'loader 不是函数错误', pattern: /### loader 不是函数错误([\s\S]*?)(?=###|---|\n## )/g },
      { name: '动态路由添加方式变更', pattern: /### 动态路由添加方式变更([\s\S]*?)(?=###|---|\n## )/g },
      { name: '路由路径格式错误', pattern: /### 路由路径格式错误([\s\S]*?)(?=###|---|\n## )/g },
      { name: 'DOM元素引用问题', pattern: /### DOM元素引用问题([\s\S]*?)(?=###|---|\n## )/g },
      { name: '组件方法无法访问', pattern: /### 组件方法无法访问([\s\S]*?)(?=###|---|\n## )/g },
      { name: '全局过滤器迁移', pattern: /### 全局过滤器迁移([\s\S]*?)(?=###|---|\n## )/g },
      { name: 'process对象未定义', pattern: /### process对象未定义([\s\S]*?)(?=###|---|\n## )/g },
      { name: 'ECharts兼容性问题', pattern: /### ECharts兼容性问题([\s\S]*?)(?=###|---|\n## )/g }
    ];

    for (const section of solutionSections) {
      let match;
      while ((match = section.pattern.exec(content)) !== null) {
        const solutionContent = match[1].trim();
        faqData.solutions.set(section.name, {
          content: solutionContent,
          summary: this.extractSolutionSummary(solutionContent)
        });
      }
    }
  }

  /**
   * 提取解决方案摘要
   */
  extractSolutionSummary(content) {
    // 提取根本原因
    const causeMatch = content.match(/\*\*根本原因：\*\*(.*?)(?=\n\*\*|$)/s);
    const cause = causeMatch ? causeMatch[1].trim() : '';

    // 提取关键解决步骤
    const steps = [];
    const stepMatches = content.match(/\d+\.\s+\*\*(.*?)\*\*/g);
    if (stepMatches) {
      steps.push(...stepMatches.map(step => step.replace(/\d+\.\s+\*\*(.*?)\*\*/, '$1')));
    }

    return {
      cause,
      steps: steps.slice(0, 3) // 只取前3个步骤
    };
  }

  /**
   * 构建错误模式映射
   */
  buildErrorPatterns() {
    if (!this.faqData) return;

    // 从错误索引构建模式
    for (const [errorKeyword, solution] of this.faqData.errorIndex) {
      this.errorPatterns.set(errorKeyword, solution);
    }

    // 添加常见错误模式
    const commonPatterns = [
      { pattern: /cannot read properties of undefined.*reading.*component/i, solution: '异步组件导入错误' },
      { pattern: /loader is not a function/i, solution: 'loader 不是函数错误' },
      { pattern: /router\.addroutes is not a function/i, solution: '动态路由添加方式变更' },
      { pattern: /route paths should start with/i, solution: '路由路径格式错误' },
      { pattern: /addeventlistener/i, solution: 'DOM元素引用问题' },
      { pattern: /is not a function.*\$refs/i, solution: '组件方法无法访问' },
      { pattern: /tothousandfilter/i, solution: '全局过滤器迁移' },
      { pattern: /process is not defined/i, solution: 'process对象未定义' },
      { pattern: /echarts.*error/i, solution: 'ECharts兼容性问题' }
    ];

    for (const { pattern, solution } of commonPatterns) {
      this.errorPatterns.set(pattern, solution);
    }
  }

  /**
   * 根据错误信息获取相关的 FAQ 建议
   */
  getErrorSuggestions(errorMessage) {
    if (!this.faqData || !errorMessage) {
      return null;
    }

    const suggestions = [];
    const lowerError = errorMessage.toLowerCase();

    // 检查直接匹配的错误关键词
    for (const [keyword, solution] of this.faqData.errorIndex) {
      if (lowerError.includes(keyword)) {
        const solutionData = this.faqData.solutions.get(solution);
        if (solutionData) {
          suggestions.push({
            type: 'direct-match',
            keyword,
            solution,
            summary: solutionData.summary,
            priority: 'high'
          });
        }
      }
    }

    // 检查模式匹配
    for (const [pattern, solution] of this.errorPatterns) {
      if (typeof pattern === 'object' && pattern.test && pattern.test(errorMessage)) {
        const solutionData = this.faqData.solutions.get(solution);
        if (solutionData && !suggestions.find(s => s.solution === solution)) {
          suggestions.push({
            type: 'pattern-match',
            pattern: pattern.toString(),
            solution,
            summary: solutionData.summary,
            priority: 'medium'
          });
        }
      }
    }

    return suggestions.length > 0 ? suggestions : null;
  }

  analyzeErrors(errors) {
    if (!this.faqData || !Array.isArray(errors)) {
      return { suggestions: [], summary: '' };
    }

    const allSuggestions = new Map();
    const errorCounts = new Map();

    for (const error of errors) {
      const errorMessage = typeof error === 'string' ? error : error.message || String(error);
      const suggestions = this.getErrorSuggestions(errorMessage);

      if (suggestions) {
        for (const suggestion of suggestions) {
          const key = suggestion.solution;
          if (!allSuggestions.has(key)) {
            allSuggestions.set(key, suggestion);
            errorCounts.set(key, 0);
          }
          errorCounts.set(key, errorCounts.get(key) + 1);
        }
      }
    }

    // 按出现频率排序
    const sortedSuggestions = Array.from(allSuggestions.values())
      .sort((a, b) => errorCounts.get(b.solution) - errorCounts.get(a.solution));

    const summary = this.generateAnalysisSummary(sortedSuggestions, errorCounts);

    return {
      suggestions: sortedSuggestions,
      summary,
      errorCounts
    };
  }

  /**
   * 生成分析摘要
   */
  generateAnalysisSummary(suggestions, errorCounts) {
    if (suggestions.length === 0) {
      return '未找到匹配的解决方案建议。';
    }

    let summary = `发现 ${suggestions.length} 类常见问题:\n`;

    for (const suggestion of suggestions.slice(0, 3)) { // 只显示前3个
      const count = errorCounts.get(suggestion.solution);
      summary += `• ${suggestion.solution} (${count} 个相关错误)\n`;
    }

    if (suggestions.length > 3) {
      summary += `... 还有 ${suggestions.length - 3} 个其他问题\n`;
    }

    return summary;
  }
}

module.exports = FAQHelper;
