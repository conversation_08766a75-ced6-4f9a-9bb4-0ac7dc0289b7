const chalk = require('chalk');
const PageValidationOrchestrator = require('./PageValidationOrchestrator');
const ValidationReportGenerator = require('./report/ValidationReportGenerator');
const RuntimeErrorHandler = require('./error/RuntimeErrorHandler');
const BuildFixAgent = require('../build-fix/BuildFixAgent');
const FAQHelper = require('./error/FAQHelper');
const ErrorAnalyzer = require('./error/ErrorAnalyzer');

/**
 * RuntimePageValidator - 重构后的页面运行时验证器
 *
 * 职责：
 * 1. 协调各个组件完成页面验证
 * 2. 管理验证流程
 * 3. 处理错误修复
 * 4. 生成验证报告
 */
class RuntimePageValidator {
  constructor(projectPath, routes, options = {}) {
    this.projectPath = projectPath;
    this.allRoutes = routes || [];
    this.routeParser = options.routeParser || null;

    // 过滤路由（如果指定了特定路由）
    if (options.specificRoutes && options.specificRoutes.length > 0) {
      this.routes = this.allRoutes.filter(route =>
        options.specificRoutes.includes(route.path)
      );
      console.log(chalk.yellow(`🎯 只验证指定的 ${this.routes.length} 个路由: ${options.specificRoutes.join(', ')}`));
    } else {
      this.routes = this.allRoutes;
    }

    this.options = {
      port: 3000,
      timeout: 30000,
      headless: 'new',
      devCommand: 'npm run dev',
      baseUrl: null,
      verbose: false,
      autoFix: false,
      maxFixAttempts: 3,
      revalidateAfterFix: true,
      dryRun: false,
      waitForServer: 60000,
      pageTimeout: 15000,
      navigationTimeout: 20000,
      routerMode: 'hash',
      loginCredentials: {
        username: 'admin',
        password: '111111'
      },
      skipLogin: false,
      ...options
    };

    this.validationResults = [];
    this.errors = [];

    // 初始化组件
    this.orchestrator = new PageValidationOrchestrator(projectPath, this.options);
    this.reportGenerator = new ValidationReportGenerator(projectPath, {
      verbose: this.options.verbose,
      includeScreenshots: true,
      includeNavigationHistory: true,
      includeFAQAnalysis: true
    });

    // 初始化错误处理相关组件
    if (this.options.autoFix) {
      this.runtimeErrorHandler = new RuntimeErrorHandler(projectPath, {
        port: this.options.port,
        autoFix: true,
        verbose: this.options.verbose
      });

      this.buildFixAgent = new BuildFixAgent(projectPath, {
        maxAttempts: this.options.maxFixAttempts || 3,
        verbose: this.options.verbose,
        dryRun: this.options.dryRun || false
      });
    }

    // 初始化FAQ助手和错误分析器
    this.faqHelper = new FAQHelper(projectPath, {
      verbose: this.options.verbose
    });

    this.errorAnalyzer = new ErrorAnalyzer({
      verbose: this.options.verbose
    });
  }

  /**
   * 验证所有页面 - 主入口方法
   */
  async validateAllPages() {
    console.log(chalk.blue(`🔍 开始验证 ${this.routes.length} 个页面...`));

    if (this.options.verbose) {
      this.printVerboseOptions();
    }

    try {
      // 1. 初始化FAQ系统
      await this.faqHelper.initialize();

      // 2. 初始化验证组件
      await this.orchestrator.initialize();

      // 3. 执行页面验证
      await this.executeValidation();

      // 4. 生成报告
      const faqAnalysis = this.faqHelper.analyzeErrors(this.getAllErrors());
      const report = this.reportGenerator.generateReport(this.validationResults, faqAnalysis);

      console.log(chalk.green(`✅ 页面验证完成`));
      this.reportGenerator.printSummary(report);

      return {
        success: true,
        results: this.validationResults,
        report: report,
        errors: this.errors
      };

    } catch (error) {
      console.error(chalk.red(`❌ 页面验证失败: ${error.message}`));
      return {
        success: false,
        results: this.validationResults,
        report: null,
        errors: [...this.errors, error.message]
      };
    } finally {
      await this.orchestrator.cleanup();
    }
  }

  /**
   * 打印详细选项信息
   */
  printVerboseOptions() {
    console.log(chalk.gray(`   详细模式已启用`));
    console.log(chalk.gray(`   页面超时: ${this.options.pageTimeout}ms`));
    console.log(chalk.gray(`   导航超时: ${this.options.navigationTimeout}ms`));
    console.log(chalk.gray(`   自动修复: ${this.options.autoFix ? '启用' : '禁用'}`));
    console.log(chalk.gray(`   路由模式: ${this.options.routerMode}`));
  }

  /**
   * 执行验证流程
   */
  async executeValidation() {
    // 1. 优先验证首页
    const homePageResult = await this.validateHomePage();

    // 2. 如果首页有错误且启用了自动修复，优先修复首页
    if (!homePageResult.success && this.options.autoFix) {
      console.log(chalk.yellow(`🏠 首页存在问题，优先修复...`));
      await this.fixPageIfNeeded(homePageResult);
    }

    // 3. 验证剩余页面
    await this.validateRemainingPages();
  }

  /**
   * 验证首页
   */
  async validateHomePage() {
    const homeRoute = this.routes.find(route => route.path === '/');

    if (!homeRoute) {
      console.log(chalk.yellow(`⚠️  未找到首页路由 (/)`));
      return { success: true, route: null };
    }

    console.log(chalk.blue(`🏠 优先验证首页: ${homeRoute.path}`));

    const result = await this.orchestrator.validateSinglePage(homeRoute);
    this.validationResults.push(result);

    this.displayPageResult(result, 1, this.routes.length);

    return result;
  }

  /**
   * 验证剩余页面
   */
  async validateRemainingPages() {
    const remainingRoutes = this.routes.filter(route => route.path !== '/');

    if (remainingRoutes.length === 0) {
      console.log(chalk.green(`✅ 只有首页需要验证，验证完成`));
      return;
    }

    // 分批处理剩余页面
    const batchSize = 10;
    const batches = this.createBatches(remainingRoutes, batchSize);

    console.log(chalk.blue(`📦 将验证 ${remainingRoutes.length} 个剩余页面，分为 ${batches.length} 批处理`));

    for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
      const batch = batches[batchIndex];
      console.log(chalk.blue(`\n📋 处理第 ${batchIndex + 1}/${batches.length} 批 (${batch.length} 个页面)`));

      await this.validateBatch(batch);

      // 批次间短暂休息
      if (batchIndex < batches.length - 1) {
        console.log(chalk.gray(`   ⏸️  批次间休息 500ms...`));
        await this.sleep(500);
      }
    }
  }

  /**
   * 验证一个批次的页面
   */
  async validateBatch(batch) {
    for (let i = 0; i < batch.length; i++) {
      const route = batch[i];
      const globalIndex = this.validationResults.length + 1;
      const totalRoutes = this.routes.length;

      console.log(chalk.gray(`   [${globalIndex}/${totalRoutes}] 验证页面: ${route.path}`));

      const result = await this.orchestrator.validateSinglePage(route);
      this.validationResults.push(result);

      this.displayPageResult(result, globalIndex, totalRoutes);

      // 如果启用自动修复且页面有错误，尝试修复
      if (!result.success && this.options.autoFix) {
        await this.fixPageIfNeeded(result);
      }

      // 页面间短暂延迟
      await this.sleep(100);
    }
  }

  /**
   * 显示页面验证结果
   */
  displayPageResult(result, currentIndex, totalRoutes) {
    if (result.success) {
      console.log(chalk.green(`   ✅ 成功`));
      if (this.options.verbose) {
        console.log(chalk.gray(`      加载时间: ${result.loadTime}ms`));
        if (result.warnings && result.warnings.length > 0) {
          console.log(chalk.yellow(`      警告: ${result.warnings.length} 个`));
        }
      }
    } else {
      console.log(chalk.red(`   ❌ 失败: ${result.errors.length} 个错误`));

      if (this.options.verbose) {
        console.log(chalk.gray(`      加载时间: ${result.loadTime}ms`));
        console.log(chalk.gray(`      URL: ${result.url}`));
        if (result.warnings && result.warnings.length > 0) {
          console.log(chalk.yellow(`      警告: ${result.warnings.length} 个`));
        }
        if (result.needsLogin) {
          console.log(chalk.yellow(`      需要登录: ${result.loginAttempted ? '已尝试' : '未尝试'}`));
        }
      }

      // 显示错误摘要
      if (!this.options.autoFix || !this.buildFixAgent) {
        this.errorAnalyzer.displayErrorSummary(result.errors, 2);
      }

      if (result.fixAttempted) {
        const fixStatus = result.fixResult?.success ? '成功' : '失败';
        console.log(chalk.yellow(`   🔧 已尝试自动修复: ${fixStatus}`));
      }
    }
  }

  /**
   * 修复页面问题（如果需要）
   */
  async fixPageIfNeeded(pageResult) {
    if (pageResult.success || !pageResult.errors || pageResult.errors.length === 0) {
      return;
    }

    console.log(chalk.yellow(`🔧 尝试修复页面的 ${pageResult.errors.length} 个错误...`));

    // 显示错误详情
    this.errorAnalyzer.displayErrorSummary(pageResult.errors, 5);

    const fixResult = await this.attemptPageErrorFix(pageResult.route, pageResult.errors);

    if (fixResult.success) {
      console.log(chalk.green(`✅ 页面错误修复成功，修复了 ${fixResult.filesModified} 个文件`));
      pageResult.fixAttempted = true;
      pageResult.fixResult = fixResult;

      // 重新验证页面
      if (this.options.revalidateAfterFix) {
        console.log(chalk.gray(`🔄 重新验证页面...`));
        const revalidationResult = await this.orchestrator.validateSinglePage(pageResult.route);

        if (revalidationResult.success) {
          console.log(chalk.green(`✅ 页面修复后验证成功`));
          Object.assign(pageResult, revalidationResult);
        } else {
          console.log(chalk.yellow(`⚠️  页面修复后仍有问题: ${revalidationResult.errors.length} 个错误`));
        }
      }
    } else {
      console.log(chalk.red(`❌ 页面错误修复失败: ${fixResult.error || '未知原因'}`));
    }
  }

  /**
   * 尝试修复页面错误
   */
  async attemptPageErrorFix(route, errors) {
    try {
      if (!this.buildFixAgent) {
        return { success: false, error: 'BuildFixAgent 未初始化' };
      }

      // 过滤出可以修复的错误
      const fixableErrors = this.errorAnalyzer.filterFixableErrors(errors);

      if (fixableErrors.length === 0) {
        return {
          success: false,
          error: '没有可修复的代码错误',
          filteredCount: errors.length - fixableErrors.length
        };
      }

      // 构建错误上下文信息
      const errorContext = this.buildPageErrorContext(route, fixableErrors);

      if (this.options.verbose) {
        console.log(chalk.gray(`    🔍 分析页面错误: ${route.path}`));
        console.log(chalk.gray(`    总错误数量: ${errors.length}, 可修复错误: ${fixableErrors.length}`));
      }

      // 分析错误并确定需要修复的文件
      const analysisResult = await this.buildFixAgent.errorAnalyzer.analyzeBuildErrors(errorContext.buildOutput, 1);

      if (!analysisResult.success || !analysisResult.filesToFix || analysisResult.filesToFix.length === 0) {
        return {
          success: false,
          error: '无法确定需要修复的文件',
          analysisResult
        };
      }

      // 执行文件修复
      const fixResult = await this.buildFixAgent.fixFiles(
        analysisResult.filesToFix,
        errorContext.buildOutput,
        1
      );

      return {
        success: fixResult.success,
        filesModified: fixResult.filesModified || 0,
        totalFiles: fixResult.totalFiles || 0,
        errors: fixResult.errors,
        analysisResult,
        fixResult
      };

    } catch (error) {
      return {
        success: false,
        error: error.message,
        stack: error.stack
      };
    }
  }

  /**
   * 构建页面错误上下文信息
   */
  buildPageErrorContext(route, errors) {
    // 使用路由解析器推断可能的组件文件路径
    let suggestedFiles = [];
    let routeComponentInfo = {};

    if (this.routeParser) {
      const errorMessages = errors.map(error => {
        if (typeof error === 'string') {
          return error;
        } else if (error.message) {
          return `${error.type || 'Error'}: ${error.message}`;
        } else {
          return JSON.stringify(error);
        }
      });

      const errorMessage = errorMessages.join(' ');
      suggestedFiles = this.routeParser.inferComponentPaths(route.path, errorMessage);

      const directComponent = this.routeParser.getComponentPathByRoute(route.path);
      if (directComponent) {
        routeComponentInfo.directMapping = directComponent;
      }
    }

    const errorContext = {
      route,
      errors,
      timestamp: new Date().toISOString(),
      suggestedFiles,
      routeComponentInfo
    };

    // 使用BuildErrorAnalyzer格式化错误输出
    const buildOutput = this.buildFixAgent.errorAnalyzer.formatRuntimeErrorAsBuildOutput(errorContext);

    return {
      buildOutput,
      route,
      errors,
      errorCount: errors.length,
      timestamp: new Date().toISOString(),
      suggestedFiles,
      routeComponentInfo
    };
  }

  /**
   * 创建批次
   */
  createBatches(routes, batchSize) {
    const batches = [];
    for (let i = 0; i < routes.length; i += batchSize) {
      batches.push(routes.slice(i, i + batchSize));
    }
    return batches;
  }

  /**
   * 获取所有错误
   */
  getAllErrors() {
    const allErrors = [];
    for (const result of this.validationResults) {
      if (result.errors && result.errors.length > 0) {
        allErrors.push(...result.errors);
      }
      if (result.loginConsoleErrors && result.loginConsoleErrors.length > 0) {
        allErrors.push(...result.loginConsoleErrors);
      }
    }
    return allErrors;
  }

  /**
   * 保存报告到文件
   */
  async saveReport(outputPath) {
    const faqAnalysis = this.faqHelper.analyzeErrors(this.getAllErrors());
    const report = this.reportGenerator.generateReport(this.validationResults, faqAnalysis);

    return await this.reportGenerator.saveMarkdownReport(report, outputPath);
  }

  /**
   * 工具方法：延迟
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

module.exports = RuntimePageValidator;
