const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const { AIService } = require('../../../ai/AiService');

/**
 * RouteConfigFixer - 路由配置修复器
 *
 * 功能：
 * 1. 自动修复路由配置错误
 * 2. 使用AI辅助复杂的修复任务
 * 3. 备份原文件
 * 4. 提供修复报告
 */
class VueRouteConfigFixer extends AIService {
  constructor(projectPath, options = {}) {
    super(options);

    this.projectPath = projectPath;
    this.options = {
      verbose: false,
      dryRun: false, // 预览模式，不实际修改文件
      createBackup: true,
      ...options
    };

    this.fixedFiles = [];
    this.fixErrors = [];
  }

  /**
   * 修复路由配置错误
   */
  async fixRouteConfigErrors(validationReport) {
    console.log(chalk.blue('🔧 开始修复路由配置错误...'));

    if (!validationReport.fixSuggestions || validationReport.fixSuggestions.length === 0) {
      console.log(chalk.yellow('⚠️  没有可修复的错误'));
      return {
        success: true,
        fixedFiles: [],
        errors: []
      };
    }

    try {
      // 按文件分组修复
      for (const suggestion of validationReport.fixSuggestions) {
        await this.fixFileErrors(suggestion);
      }

      const report = this.generateFixReport();

      if (this.fixErrors.length === 0) {
        console.log(chalk.green(`✅ 成功修复 ${this.fixedFiles.length} 个文件`));
      } else {
        console.log(chalk.yellow(`⚠️  修复完成，但有 ${this.fixErrors.length} 个错误`));
      }

      return report;

    } catch (error) {
      console.error(chalk.red(`❌ 修复过程失败: ${error.message}`));
      return {
        success: false,
        fixedFiles: this.fixedFiles,
        errors: [...this.fixErrors, error.message]
      };
    }
  }

  /**
   * 修复单个文件的错误
   */
  async fixFileErrors(suggestion) {
    const filePath = path.join(this.projectPath, suggestion.file);

    try {
      if (this.options.verbose) {
        console.log(chalk.gray(`   修复文件: ${suggestion.file}`));
      }

      // 读取原文件内容
      const originalContent = await fs.readFile(filePath, 'utf8');

      // 创建备份
      if (this.options.createBackup && !this.options.dryRun) {
        await this.createBackup(filePath);
      }

      // 根据修复类型选择修复方法
      let fixedContent;

      switch (suggestion.type) {
        case 'fix-path-format':
        case 'fix-wildcard-route':
          fixedContent = await this.fixPathFormatErrors(originalContent, suggestion);
          break;
        default:
          // 使用AI修复复杂问题
          fixedContent = await this.fixWithAI(originalContent, suggestion);
          break;
      }

      if (fixedContent && fixedContent !== originalContent) {
        if (this.options.dryRun) {
          console.log(chalk.blue(`   [预览] 将修复文件: ${suggestion.file}`));
          if (this.options.verbose) {
            this.showDiff(originalContent, fixedContent);
          }
        } else {
          // 写入修复后的内容
          await fs.writeFile(filePath, fixedContent, 'utf8');
          console.log(chalk.green(`   ✅ 已修复: ${suggestion.file}`));
        }

        this.fixedFiles.push({
          file: suggestion.file,
          type: suggestion.type,
          description: suggestion.description,
          changes: suggestion.changes
        });
      } else {
        if (this.options.verbose) {
          console.log(chalk.gray(`   ⚪ 无需修复: ${suggestion.file}`));
        }
      }

    } catch (error) {
      this.fixErrors.push({
        file: suggestion.file,
        error: error.message
      });

      if (this.options.verbose) {
        console.log(chalk.red(`   ❌ 修复失败: ${suggestion.file} - ${error.message}`));
      }
    }
  }

  /**
   * 修复路径格式错误
   */
  async fixPathFormatErrors(content, suggestion) {
    let fixedContent = content;

    // 按行号倒序处理，避免行号偏移问题
    const sortedChanges = suggestion.changes.sort((a, b) => (b.line || 0) - (a.line || 0));

    for (const change of sortedChanges) {
      // 智能字符串替换，处理单引号和双引号
      const patterns = [
        change.from, // 原始模式
        change.from.replace(/"/g, "'"), // 双引号转单引号
        change.from.replace(/'/g, '"'), // 单引号转双引号
      ];

      let replaced = false;
      for (const pattern of patterns) {
        if (fixedContent.includes(pattern)) {
          const replacement = change.to.replace(/"/g, pattern.includes("'") ? "'" : '"');
          fixedContent = fixedContent.replace(pattern, replacement);
          replaced = true;

          if (this.options.verbose) {
            console.log(chalk.green(`     ✅ 替换: ${pattern} → ${replacement}`));
          }
          break;
        }
      }

      if (!replaced && this.options.verbose) {
        console.log(chalk.yellow(`     ⚠️  未找到匹配: ${change.from}`));
      }
    }

    return fixedContent;
  }

  /**
   * 使用AI修复复杂问题
   */
  async fixWithAI(content, suggestion) {
    if (!this.isEnabled()) {
      if (this.options.verbose) {
        console.log(chalk.yellow('   ⚠️  AI服务未启用，跳过AI修复'));
      }
      return content;
    }

    const prompt = `
你是一个Vue Router配置修复专家。请修复以下路由配置文件中的问题。

文件: ${suggestion.file}
问题描述: ${suggestion.description}

需要修复的变更:
${suggestion.changes.map(change => 
  `- 第${change.line}行: ${change.from} → ${change.to}`
).join('\n')}

原始代码:
\`\`\`javascript
${content}
\`\`\`

请返回修复后的完整代码，确保：
1. 修复所有指定的问题
2. 保持代码格式和结构不变
3. 不要添加或删除其他内容
4. 确保语法正确

只返回修复后的代码，不要添加任何解释或注释。
`;

    try {
      const response = await this.callAI(prompt, {
        context: {
          taskType: 'route-config-fix',
          fileName: suggestion.file
        }
      });

      // 提取代码块
      const codeMatch = response.match(/```(?:javascript|js)?\s*([\s\S]*?)\s*```/);
      if (codeMatch) {
        return codeMatch[1];
      }

      // 如果没有代码块，返回原内容
      return content;

    } catch (error) {
      if (this.options.verbose) {
        console.log(chalk.yellow(`   ⚠️  AI修复失败: ${error.message}`));
      }
      return content;
    }
  }

  /**
   * 创建文件备份
   */
  async createBackup(filePath) {
    const backupPath = `${filePath}.backup.${Date.now()}`;
    await fs.copy(filePath, backupPath);

    if (this.options.verbose) {
      console.log(chalk.gray(`   📁 已创建备份: ${path.basename(backupPath)}`));
    }
  }

  /**
   * 显示文件差异
   */
  showDiff(original, fixed) {
    const originalLines = original.split('\n');
    const fixedLines = fixed.split('\n');

    console.log(chalk.blue('   📝 修复预览:'));

    const maxLines = Math.max(originalLines.length, fixedLines.length);
    let diffCount = 0;

    for (let i = 0; i < maxLines && diffCount < 10; i++) {
      const originalLine = originalLines[i] || '';
      const fixedLine = fixedLines[i] || '';

      if (originalLine !== fixedLine) {
        console.log(chalk.red(`   - ${i + 1}: ${originalLine}`));
        console.log(chalk.green(`   + ${i + 1}: ${fixedLine}`));
        diffCount++;
      }
    }

    if (diffCount >= 10) {
      console.log(chalk.gray('   ... (更多差异)'));
    }
  }

  /**
   * 生成修复报告
   */
  generateFixReport() {
    return {
      success: this.fixErrors.length === 0,
      fixedFiles: this.fixedFiles,
      errors: this.fixErrors,
      summary: {
        totalFixed: this.fixedFiles.length,
        totalErrors: this.fixErrors.length,
        dryRun: this.options.dryRun
      }
    };
  }
}

module.exports = VueRouteConfigFixer;
