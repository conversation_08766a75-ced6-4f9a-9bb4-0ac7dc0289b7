const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const SassVariableRegistry = require('./SassVariableRegistry');

/**
 * Sass 变量解析器
 * 负责处理所有与变量相关的逻辑，包括检测、分析和自动导入
 */
class SassVariableResolver {
  constructor(projectPath, options = {}) {
    this.projectPath = path.resolve(projectPath);
    this.options = {
      verbose: false,
      autoImportVariables: true,
      preferUseOverImport: true,
      ...options
    };
    
    this.registry = new SassVariableRegistry(this.projectPath);
    this.isInitialized = false;
  }

  /**
   * 初始化变量注册表
   */
  async initialize() {
    if (!this.isInitialized && this.options.autoImportVariables) {
      if (this.options.verbose) {
        console.log(chalk.blue('🔍 初始化变量解析器...'));
      }
      
      const success = await this.registry.buildRegistry();
      this.isInitialized = success;
      
      if (!success) {
        console.log(chalk.yellow('⚠️  变量注册表初始化失败，将跳过自动变量导入功能'));
      }
      
      return success;
    }
    
    return this.isInitialized;
  }

  /**
   * 检测文件中使用的变量（排除本地定义的变量）
   */
  detectUsedVariables(content) {
    const usedVariables = new Set();
    
    // 移除注释和字符串，避免误检测
    const cleanContent = this.removeCommentsAndStrings(content);
    
    // 获取本地定义的变量
    const localVariables = this.detectDefinedVariables(content);
    
    // 检测变量使用，但排除变量定义
    const lines = cleanContent.split('\n');
    
    for (const line of lines) {
      // 跳过变量定义行
      if (this.isVariableDefinitionLine(line)) {
        continue;
      }
      
      // 匹配变量使用
      const variableMatches = line.match(/\$([a-zA-Z0-9_-]+)/g);
      if (variableMatches) {
        for (const match of variableMatches) {
          const variableName = match.substring(1); // 移除$符号
          
          // 只添加非本地定义的变量
          if (!localVariables.has(variableName)) {
            usedVariables.add(variableName);
          }
        }
      }
    }

    return [...usedVariables];
  }

  /**
   * 检测文件中定义的变量
   */
  detectDefinedVariables(content) {
    const definedVariables = new Set();
    const variableDefRegex = /\$([a-zA-Z0-9_-]+)\s*:/g;
    let match;

    while ((match = variableDefRegex.exec(content)) !== null) {
      definedVariables.add(match[1]);
    }

    return definedVariables;
  }

  /**
   * 检测文件中已有的导入语句
   */
  detectExistingImports(content) {
    const importRegex = /@(?:import|use)\s+['"]([^'"]+)['"](?:\s+as\s+[^;]*)?;?/g;
    const imports = new Set();
    let match;

    while ((match = importRegex.exec(content)) !== null) {
      imports.add(match[1]);
    }

    return imports;
  }

  /**
   * 分析文件的变量依赖
   */
  async analyzeFileDependencies(filePath) {
    try {
      const content = await fs.readFile(filePath, 'utf8');
      const usedVariables = this.detectUsedVariables(content);
      const definedVariables = this.detectDefinedVariables(content);
      const existingImports = this.detectExistingImports(content);

      if (!this.isInitialized) {
        await this.initialize();
      }

      const missingVariables = [];
      const availableVariables = [];

      for (const variable of usedVariables) {
        if (this.registry.hasVariable(variable)) {
          availableVariables.push(variable);
        } else {
          missingVariables.push(variable);
        }
      }

      return {
        filePath,
        usedVariables,
        definedVariables: [...definedVariables],
        existingImports: [...existingImports],
        availableVariables,
        missingVariables,
        needsImport: availableVariables.length > 0
      };
    } catch (error) {
      console.warn(chalk.yellow(`分析文件失败 ${filePath}: ${error.message}`));
      return null;
    }
  }

  /**
   * 生成缺失变量的导入建议
   */
  generateImportSuggestions(filePath, usedVariables) {
    if (!this.isInitialized) {
      return [];
    }

    return this.registry.generateImportSuggestions(filePath, usedVariables);
  }

  /**
   * 自动添加缺失的变量导入
   */
  async autoAddVariableImports(content, filePath) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    if (!this.isInitialized) {
      return { content, importsAdded: 0, suggestions: [] };
    }

    const usedVariables = this.detectUsedVariables(content);
    const existingImports = this.detectExistingImports(content);
    
    if (usedVariables.length === 0) {
      return { content, importsAdded: 0, suggestions: [] };
    }

    const suggestions = this.generateImportSuggestions(filePath, usedVariables);
    const newImports = [];

    for (const suggestion of suggestions) {
      // 检查是否已经导入了这个文件
      const alreadyImported = [...existingImports].some(existing => {
        // 规范化路径进行比较
        const existingNormalized = existing.replace(/\.scss$/, '');
        const suggestionNormalized = suggestion.importPath.replace(/\.scss$/, '');
        return existingNormalized === suggestionNormalized;
      });

      if (!alreadyImported) {
        newImports.push(suggestion);
        
        if (this.options.verbose) {
          const variableList = suggestion.variables.join(', ');
          console.log(chalk.gray(`    📦 添加变量导入: ${suggestion.importPath} (变量: ${variableList})`));
        }
      }
    }

    if (newImports.length > 0) {
      // 在文件开头添加导入语句
      const importStatements = newImports.map(imp => imp.statement).join('\n');
      const importsSection = importStatements + '\n\n';
      content = importsSection + content;
    }

    return { 
      content, 
      importsAdded: newImports.length, 
      suggestions: newImports 
    };
  }

  /**
   * 检查项目中的变量使用问题
   */
  async checkVariableIssues(sassFiles) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const issues = [];
    const analysisResults = [];

    for (const filePath of sassFiles) {
      const analysis = await this.analyzeFileDependencies(filePath);
      if (analysis) {
        analysisResults.push(analysis);

        // 检查缺失的变量
        if (analysis.missingVariables.length > 0) {
          issues.push({
            type: 'undefined_variables',
            file: filePath,
            variables: analysis.missingVariables,
            severity: 'error'
          });
        }

        // 检查缺失的导入
        if (analysis.needsImport && analysis.availableVariables.length > 0) {
          const suggestions = this.generateImportSuggestions(filePath, analysis.availableVariables);
          const missingImports = suggestions.filter(suggestion => {
            return !analysis.existingImports.some(existing => {
              const existingNormalized = existing.replace(/\.scss$/, '');
              const suggestionNormalized = suggestion.importPath.replace(/\.scss$/, '');
              return existingNormalized === suggestionNormalized;
            });
          });

          if (missingImports.length > 0) {
            issues.push({
              type: 'missing_imports',
              file: filePath,
              variables: analysis.availableVariables,
              suggestions: missingImports,
              severity: 'warning'
            });
          }
        }
      }
    }

    return {
      issues,
      analysisResults,
      summary: {
        totalFiles: sassFiles.length,
        filesWithIssues: issues.length,
        undefinedVariables: issues.filter(i => i.type === 'undefined_variables').length,
        missingImports: issues.filter(i => i.type === 'missing_imports').length
      }
    };
  }

  /**
   * 移除注释和字符串内容
   */
  removeCommentsAndStrings(content) {
    return content
      .replace(/\/\*[\s\S]*?\*\//g, '') // 块注释
      .replace(/\/\/.*$/gm, '') // 行注释
      .replace(/'[^']*'/g, '""') // 单引号字符串
      .replace(/"[^"]*"/g, '""'); // 双引号字符串
  }

  /**
   * 判断是否为变量定义行
   */
  isVariableDefinitionLine(line) {
    const trimmed = line.trim();
    return /\$[a-zA-Z0-9_-]+\s*:/.test(trimmed);
  }

  /**
   * 获取变量注册表统计信息
   */
  getRegistryStats() {
    if (!this.isInitialized) {
      return null;
    }

    return {
      totalVariables: this.registry.variables.size,
      centralFiles: this.registry.centralVariableFiles.length,
      variableFiles: this.registry.fileVariables.size
    };
  }
}

module.exports = SassVariableResolver;
