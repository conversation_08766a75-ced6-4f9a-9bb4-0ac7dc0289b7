const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');

/**
 * 智能路径解析器
 * 处理 Sass 迁移中的各种路径解析问题
 */
class ElementPlusSassPathResolver {
  constructor(projectPath, options = {}) {
    this.projectPath = path.resolve(projectPath);
    this.nodeModulesPath = path.join(this.projectPath, 'node_modules');
    this.options = {
      viteConfigPath: path.join(this.projectPath, 'vite.config.js'),
      verbose: false,
      ...options
    };

    this.pathMappings = new Map();

    // 预定义的库路径映射
    this.libraryMappings = {
      '~element-ui': {
        'packages/theme-chalk/src/index': 'element-plus/theme-chalk/src/index.scss',
        'lib/theme-chalk/index.css': 'element-plus/dist/index.css',
        'packages/theme-chalk/src/': 'element-plus/theme-chalk/src/'
      },
      'element-ui': {
        'packages/theme-chalk/src/index': 'element-plus/theme-chalk/src/index.scss',
        'lib/theme-chalk/index.css': 'element-plus/dist/index.css',
        'packages/theme-chalk/src/': 'element-plus/theme-chalk/src/'
      },
      'bootstrap': {
        'scss/bootstrap': 'bootstrap/scss/bootstrap'
      }
    };
  }

  resolvePath(importPath, currentFilePath) {
    const originalPath = importPath;

    const migratedPath = this.resolveLibraryMigration(importPath);
    if (migratedPath !== importPath) {
      importPath = this.ensureExtension(migratedPath);
      if (this.options.verbose) {
        console.log(chalk.gray(`路径解析: ${originalPath} -> ${importPath}`));
      }
      return importPath;
    }

    // 2. 处理 ~ 别名
    if (importPath.startsWith('~')) {
      importPath = this.resolveTildeAlias(importPath);
    }

    // 3. 处理其他别名
    importPath = this.resolveAlias(importPath);

    // 4. 处理相对路径
    if (importPath.startsWith('./') || importPath.startsWith('../')) {
      importPath = this.resolveRelativePath(importPath, currentFilePath);
    }

    // 5. 确保文件扩展名
    importPath = this.ensureExtension(importPath);

    if (this.options.verbose && importPath !== originalPath) {
      console.log(chalk.gray(`路径解析: ${originalPath} -> ${importPath}`));
    }

    return importPath
  }

  /**
   * 解析 ~ 别名
   */
  resolveTildeAlias(importPath) {
    if (importPath.startsWith('~')) {
      const resolved = path.join(this.nodeModulesPath, importPath.slice(1));
      return this.normalizePath(resolved);
    }
    return importPath;
  }

  /**
   * 解析其他别名
   */
  resolveAlias(importPath) {
    for (const [alias, target] of this.pathMappings) {
      if (importPath.startsWith(alias + '/') || importPath === alias) {
        return importPath.replace(alias, target);
      }
    }
    return importPath;
  }

  /**
   * 解析库迁移
   */
  resolveLibraryMigration(importPath) {
    const cleanPath = importPath.startsWith('~') ? importPath.slice(1) : importPath;
    for (const [oldLib, mappings] of Object.entries(this.libraryMappings)) {
      if (cleanPath.includes(oldLib)) {
        for (const [oldPath, newPath] of Object.entries(mappings)) {
          if (cleanPath.includes(oldPath)) {
            // 如果是目录映射（以 / 结尾），需要保留后续路径
            if (oldPath.endsWith('/') && newPath.endsWith('/')) {
              const remainingPath = cleanPath.substring(cleanPath.indexOf(oldPath) + oldPath.length);
              return newPath + remainingPath;
            }
            return newPath;
          }
        }
      }
    }

    return importPath;
  }

  /**
   * 解析相对路径
   */
  resolveRelativePath(importPath, currentFilePath) {
    if (!currentFilePath) {
      return importPath;
    }

    const currentDir = path.dirname(currentFilePath);
    const resolved = path.resolve(currentDir, importPath);

    // 返回相对于项目根目录的路径
    let relativePath = path.relative(this.projectPath, resolved);

    // 确保在所有平台上都使用正斜杠，因为 Sass 导入路径需要使用正斜杠
    return this.normalizePath(relativePath);
  }

  /**
   * 确保文件扩展名
   */
  ensureExtension(importPath) {
    const ext = path.extname(importPath);
    if (!ext && !importPath.endsWith('/')) {
      return importPath + '.scss';
    }
    return importPath;
  }

  /**
   * 规范化路径，确保在所有平台上都使用正斜杠
   * 这对于 Sass 导入路径很重要，因为 Sass 期望使用正斜杠
   */
  normalizePath(filePath) {
    if (!filePath) {
      return filePath;
    }
    // 将所有反斜杠替换为正斜杠
    return filePath.replace(/\\/g, '/');
  }

  /**
   * 转换 @import 为 @use
   */
  convertImportToUse(importStatement, currentFilePath) {
    const importRegex = /@import\s+['"]([^'"]+)['"](?:\s*;)?/;
    const match = importStatement.match(importRegex);

    if (!match) {
      return importStatement;
    }

    const importPath = match[1];
    const resolvedPath = this.resolvePath(importPath, currentFilePath);

    // 生成命名空间
    const namespace = this.generateNamespace(resolvedPath);

    // 检查是否需要 as *
    const needsGlobalAccess = this.shouldUseGlobalAccess(importPath);

    if (needsGlobalAccess) {
      return `@use '${resolvedPath}' as *;`;
    } else {
      return `@use '${resolvedPath}' as ${namespace};`;
    }
  }

  /**
   * 生成命名空间
   */
  generateNamespace(importPath) {
    const basename = path.basename(importPath, path.extname(importPath));
    const cleanName = basename.startsWith('_') ? basename.slice(1) : basename;
    return cleanName.replace(/[^a-zA-Z0-9_-]/g, '-');
  }

  /**
   * 判断是否应该使用全局访问 (as *)
   */
  shouldUseGlobalAccess(importPath) {
    // 对于项目内部的工具文件，可以使用 as *
    const internalPatterns = [
      'variables',
      'mixins',
      'functions',
      'utils',
      'src/styles'
    ];

    // 对于 Element Plus 等外部库，也使用 as *
    const externalGlobalPatterns = [
      'element-plus',
      'element-ui'
    ];

    return internalPatterns.some(pattern => importPath.includes(pattern)) ||
           externalGlobalPatterns.some(pattern => importPath.includes(pattern));
  }
}

module.exports = ElementPlusSassPathResolver;
