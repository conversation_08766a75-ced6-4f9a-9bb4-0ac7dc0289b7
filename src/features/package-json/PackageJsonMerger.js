const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const ComponentDependencyMapper = require('../../frameworks/vue/third-party/ComponentDependencyMapper');

/**
 * Package.json 合并器
 * 负责智能合并源项目和目标项目的 package.json，处理依赖映射和版本冲突
 */
class PackageJsonMerger {
  constructor(sourceProjectPath, targetProjectPath, options = {}) {
    this.sourceProjectPath = sourceProjectPath;
    this.targetProjectPath = targetProjectPath;
    this.options = {
      preserveTargetDependencies: true, // 保留目标项目的现有依赖
      enableThirdPartyMapping: true, // 启用第三方组件映射
      verbose: false,
      dryRun: false,
      ...options
    };

    // 初始化依赖映射器
    this.dependencyMapper = new ComponentDependencyMapper();

    // 加载配置
    this.configPath = path.join(__dirname, '../../../config/package-recommend.json');
    this.config = null;

    // Vue 2 官方依赖列表（这些依赖在 Vue 3 中不需要）
    this.vue2OfficialDeps = new Set([
      'vue-template-compiler',
      '@vue/composition-api',
      'vue-class-component',
      'vue-property-decorator',
      'vuex-class'
    ]);
  }

  /**
   * 加载配置文件
   */
  async loadConfig() {
    try {
      this.config = await fs.readJson(this.configPath);
    } catch (error) {
      console.error(chalk.red('❌ 无法加载配置文件:'), error.message);
      throw new Error(`Failed to load config file: ${this.configPath}`);
    }
  }

  /**
   * 执行 package.json 合并
   */
  async merge() {
    console.log(chalk.blue('📦 开始合并 package.json...'));

    try {
      // 加载配置
      await this.loadConfig();

      // 读取源项目和目标项目的 package.json
      const sourcePackageJson = await this.readPackageJson(this.sourceProjectPath);
      const targetPackageJson = await this.readPackageJson(this.targetProjectPath);

      if (!sourcePackageJson) {
        throw new Error(`源项目中未找到 package.json: ${this.sourceProjectPath}`);
      }

      if (!targetPackageJson) {
        throw new Error(`目标项目中未找到 package.json: ${this.targetProjectPath}`);
      }

      // 执行智能合并
      const mergedPackageJson = await this.performIntelligentMerge(sourcePackageJson, targetPackageJson);

      // 写入合并后的 package.json
      if (!this.options.dryRun) {
        const targetPackageJsonPath = path.join(this.targetProjectPath, 'package.json');
        await fs.writeJson(targetPackageJsonPath, mergedPackageJson, { spaces: 2 });
      }

      console.log(chalk.green('✅ package.json 合并完成!'));

      return {
        success: true,
        mergedPackageJson: this.options.dryRun ? mergedPackageJson : null,
        changes: this.generateChangesSummary(sourcePackageJson, targetPackageJson, mergedPackageJson)
      };
    } catch (error) {
      console.error(chalk.red('❌ package.json 合并失败:'), error.message);
      throw error;
    }
  }

  /**
   * 读取 package.json 文件
   */
  async readPackageJson(projectPath) {
    try {
      const packageJsonPath = path.join(projectPath, 'package.json');
      if (await fs.pathExists(packageJsonPath)) {
        return await fs.readJson(packageJsonPath);
      }
      return null;
    } catch (error) {
      console.warn(chalk.yellow(`读取 package.json 失败: ${error.message}`));
      return null;
    }
  }

  /**
   * 执行智能合并
   */
  async performIntelligentMerge(sourcePackageJson, targetPackageJson) {
    // 以目标项目的 package.json 为基础
    const mergedPackageJson = JSON.parse(JSON.stringify(targetPackageJson));

    // 合并基本信息（保留目标项目的信息，但可以从源项目补充缺失的字段）
    this.mergeBasicInfo(sourcePackageJson, mergedPackageJson);

    // 合并 scripts（智能合并，避免覆盖重要的构建脚本）
    this.mergeScripts(sourcePackageJson, mergedPackageJson);

    // 合并依赖（最复杂的部分）
    await this.mergeDependencies(sourcePackageJson, mergedPackageJson);

    // 合并其他字段
    this.mergeOtherFields(sourcePackageJson, mergedPackageJson);

    return mergedPackageJson;
  }

  /**
   * 合并基本信息
   */
  mergeBasicInfo(sourcePackageJson, mergedPackageJson) {
    // 保留目标项目的 name, version, description
    // 但如果目标项目缺少这些字段，可以从源项目补充
    const fieldsToMerge = ['description', 'keywords', 'author', 'license', 'repository', 'bugs', 'homepage'];

    fieldsToMerge.forEach(field => {
      if (!mergedPackageJson[field] && sourcePackageJson[field]) {
        mergedPackageJson[field] = sourcePackageJson[field];
        if (this.options.verbose) {
          console.log(chalk.gray(`  补充字段 ${field}: ${JSON.stringify(sourcePackageJson[field])}`));
        }
      }
    });
  }

  /**
   * 合并 scripts
   */
  mergeScripts(sourcePackageJson, mergedPackageJson) {
    if (!sourcePackageJson.scripts) return;

    if (!mergedPackageJson.scripts) {
      mergedPackageJson.scripts = {};
    }

    // 保留目标项目的重要脚本，但可以从源项目添加缺失的脚本
    const importantScripts = new Set(['serve', 'build', 'dev', 'start', 'test', 'lint']);

    Object.entries(sourcePackageJson.scripts).forEach(([scriptName, scriptCommand]) => {
      if (!mergedPackageJson.scripts[scriptName]) {
        // 如果目标项目没有这个脚本，添加它
        mergedPackageJson.scripts[scriptName] = scriptCommand;
        if (this.options.verbose) {
          console.log(chalk.gray(`  添加脚本 ${scriptName}: ${scriptCommand}`));
        }
      } else if (!importantScripts.has(scriptName)) {
        // 如果不是重要脚本，可以考虑覆盖
        if (this.options.verbose) {
          console.log(chalk.gray(`  保留目标项目的脚本 ${scriptName}`));
        }
      }
    });
  }

  /**
   * 合并依赖（核心逻辑）
   */
  async mergeDependencies(sourcePackageJson, mergedPackageJson) {
    console.log(chalk.gray('  🔄 智能合并依赖...'));

    // 处理 dependencies
    if (sourcePackageJson.dependencies) {
      await this.mergeDependencySection(
        sourcePackageJson.dependencies,
        mergedPackageJson,
        'dependencies'
      );
    }

    // 处理 devDependencies
    if (sourcePackageJson.devDependencies) {
      await this.mergeDependencySection(
        sourcePackageJson.devDependencies,
        mergedPackageJson,
        'devDependencies'
      );
    }
  }

  /**
   * 合并依赖部分
   */
  async mergeDependencySection(sourceDeps, mergedPackageJson, section) {
    if (!mergedPackageJson[section]) {
      mergedPackageJson[section] = {};
    }

    const targetDeps = mergedPackageJson[section];
    const changes = [];

    for (const [depName, depVersion] of Object.entries(sourceDeps)) {
      // 跳过 Vue 2 官方依赖
      if (this.vue2OfficialDeps.has(depName)) {
        if (this.options.verbose) {
          console.log(chalk.gray(`  跳过 Vue 2 官方依赖: ${depName}`));
        }
        continue;
      }

      // 检查是否需要进行第三方组件映射
      if (this.options.enableThirdPartyMapping && this.dependencyMapper.hasMapping(depName)) {
        const mapping = this.dependencyMapper.getTargetDependency(depName);
        const targetDepName = mapping.target;

        if (!targetDeps[targetDepName]) {
          targetDeps[targetDepName] = 'latest'; // 或者从映射中获取推荐版本
          changes.push(`映射 ${depName} -> ${targetDepName}`);
          if (this.options.verbose) {
            console.log(chalk.green(`  映射依赖: ${depName} -> ${targetDepName}`));
          }
        }
        continue;
      }

      // 检查是否在已知不兼容列表中
      if (this.config.knownIncompatible[depName]) {
        const incompatible = this.config.knownIncompatible[depName];
        if (incompatible.alternatives && incompatible.alternatives.length > 0) {
          const alternative = incompatible.alternatives[0];
          if (!targetDeps[alternative] && this.config.knownCompatible[alternative]) {
            targetDeps[alternative] = this.config.knownCompatible[alternative].version || 'latest';
            changes.push(`替换不兼容依赖 ${depName} -> ${alternative}`);
            if (this.options.verbose) {
              console.log(chalk.yellow(`  替换不兼容依赖: ${depName} -> ${alternative}`));
            }
          }
        }
        continue;
      }

      // 检查目标项目是否已有此依赖
      if (targetDeps[depName]) {
        if (this.options.preserveTargetDependencies) {
          // 保留目标项目的版本
          if (this.options.verbose) {
            console.log(chalk.gray(`  保留目标依赖: ${depName}@${targetDeps[depName]}`));
          }
        } else {
          // 使用源项目的版本
          targetDeps[depName] = depVersion;
          changes.push(`更新 ${depName}: ${targetDeps[depName]} -> ${depVersion}`);
        }
      } else {
        // 目标项目没有此依赖，检查是否应该添加
        if (this.shouldAddDependency(depName, depVersion)) {
          targetDeps[depName] = depVersion;
          changes.push(`添加 ${depName}@${depVersion}`);
          if (this.options.verbose) {
            console.log(chalk.green(`  添加依赖: ${depName}@${depVersion}`));
          }
        }
      }
    }

    return changes;
  }

  /**
   * 判断是否应该添加依赖
   */
  shouldAddDependency(depName, depVersion) {
    // 检查是否在已知兼容列表中
    if (this.config.knownCompatible[depName]) {
      return true;
    }

    // 检查是否在需要升级列表中
    if (this.config.needsUpgrade[depName]) {
      return true;
    }

    // 检查是否在系统依赖列表中（通常不需要添加）
    if (this.config.systemDependencies.includes(depName)) {
      return false;
    }

    // 默认添加（但会在后续步骤中进行兼容性检查）
    return true;
  }

  /**
   * 合并其他字段
   */
  mergeOtherFields(sourcePackageJson, mergedPackageJson) {
    const fieldsToMerge = ['browserslist', 'engines', 'os', 'cpu'];

    fieldsToMerge.forEach(field => {
      if (sourcePackageJson[field] && !mergedPackageJson[field]) {
        mergedPackageJson[field] = sourcePackageJson[field];
        if (this.options.verbose) {
          console.log(chalk.gray(`  添加字段 ${field}`));
        }
      }
    });
  }

  /**
   * 生成变更摘要
   */
  generateChangesSummary(sourcePackageJson, targetPackageJson, mergedPackageJson) {
    const changes = [];

    // 比较依赖变化
    const sections = ['dependencies', 'devDependencies'];
    sections.forEach(section => {
      if (mergedPackageJson[section] && targetPackageJson[section]) {
        const merged = mergedPackageJson[section];
        const target = targetPackageJson[section];

        Object.keys(merged).forEach(depName => {
          if (!target[depName]) {
            changes.push(`添加 ${section}: ${depName}@${merged[depName]}`);
          } else if (target[depName] !== merged[depName]) {
            changes.push(`更新 ${section}: ${depName} ${target[depName]} -> ${merged[depName]}`);
          }
        });
      }
    });

    return changes;
  }

}

module.exports = PackageJsonMerger;
