const chalk = require('chalk');

/**
 * 字符串替换工具类
 * 负责生成字符串替换提示词和解析AI响应
 * 解决了原有的提示词格式与解析逻辑不一致的问题
 */
class StringReplaceTool {
  constructor(options = {}) {
    this.options = {
      verbose: false,
      ...options
    };
  }

  /**
   * 生成字符串替换提示词
   * @param {string} filePath - 文件路径
   * @param {string} fileContent - 文件内容
   * @param {string} buildOutput - 构建错误输出
   * @param {Object} contextFiles - 上下文文件
   * @param {Object} context - 上下文信息
   * @param {Array} suggestions - 修复建议
   * @returns {string} 生成的提示词
   */
  generatePrompt(filePath, fileContent, buildOutput, contextFiles = {}, context = {}, suggestions = []) {
    const truncatedOutput = this.truncateOutput(buildOutput);

    return `你是一个专业的 Vue 2 迁移 Vue 3 的代码修复专家。当前文件较大，请使用 str_replace 工具进行精确的局部修复。

目标项目的技术栈是：Webpack 5 + Vue 3 + Element Plus + Vuex 4 + Vue Router 4。

**你要修改的文件路径**: ${filePath}
**尝试次数**: 第 ${context.attemptNumber || 1} 次

**文件内容**：
${fileContent}

**这个文件的相关构建错误输出**：
\`\`\`
${truncatedOutput}
\`\`\`

**相关文件内容**：
${this.formatContextFiles(contextFiles)}

**之前的尝试记录**：
${this.formatPreviousAttempts(context.previousAttempts || [])}

**修复建议**：
${JSON.stringify(suggestions, null, 2)}

**任务要求**：
1. 分析构建错误，定位需要修复的具体代码段
2. 使用 str_replace 工具进行精确替换，避免返回完整文件内容
3. 确保 old_string 包含足够的上下文（控制在 5~10 行）以保证唯一匹配
4. 保持正确的缩进和代码格式
5. 专注于 Vue 2 到 Vue 3 的迁移问题

**响应格式**：
使用标准的工具调用格式：

\`\`\`xml
<tool_call>
<name>str_replace</name>
<parameters>
<file_path>${filePath}</file_path>
<old_string>
要替换的原始代码段（包含足够上下文）
</old_string>
<new_string>
修复后的代码段
</new_string>
<expected_replacements>1</expected_replacements>
</parameters>
</tool_call>
\`\`\`

**重要提示**：
- old_string 必须完全匹配原文件中的内容（包括空格、缩进、换行符）
- 包含前后足够的上下文代码以确保唯一匹配
- 如果需要多处修改，可以使用多个 tool_call
- 确保修复后的代码符合 Vue 3 的语法和最佳实践
- 专注于修复具体错误（Error），deprecated，warning 等就不要管了，不要进行不必要的重构

请开始分析并修复代码：`;
  }

  /**
   * 解析字符串替换响应
   * @param {string} response - AI响应
   * @param {string} originalContent - 原始文件内容
   * @returns {Object} 解析结果
   */
  parseResponse(response, originalContent) {
    try {
      if (this.options.verbose) {
        console.log(chalk.gray('    🔍 开始解析 str_replace 响应...'));
        console.log(chalk.gray(`       响应长度: ${response.length} 字符`));
      }

      // 解析标准工具调用格式
      const toolCallMatch = response.match(/<tool_call>\s*<(?:name|n)>str_replace<\/(?:name|n)>\s*<parameters>([\s\S]*?)<\/parameters>\s*<\/tool_call>/);

      if (toolCallMatch) {
        return this.parseToolCallFormat(toolCallMatch[1], response);
      }

      // 回退：尝试解析简化格式（向后兼容）
      const simpleMatch = response.match(/<str_replace>\s*<old_string>([\s\S]*?)<\/old_string>\s*<new_string>([\s\S]*?)<\/new_string>\s*<\/str_replace>/);

      if (simpleMatch) {
        if (this.options.verbose) {
          console.log(chalk.gray('       ⚠️  检测到旧格式，建议更新提示词'));
        }
        return this.parseSimpleFormat(simpleMatch, response);
      }

      // 如果都没有匹配，返回失败
      return {
        success: false,
        error: '未找到有效的 str_replace 工具调用格式',
        aiResponse: response
      };

    } catch (error) {
      return {
        success: false,
        error: `解析 str_replace 响应失败: ${error.message}`,
        aiResponse: response
      };
    }
  }

  /**
   * 解析标准工具调用格式
   * @param {string} parametersText - 参数文本
   * @param {string} fullResponse - 完整响应
   * @returns {Object} 解析结果
   */
  parseToolCallFormat(parametersText, fullResponse) {
    try {
      // 解析参数
      const filePathMatch = parametersText.match(/<file_path>([\s\S]*?)<\/file_path>/);
      const oldStringMatch = parametersText.match(/<old_string>([\s\S]*?)<\/old_string>/);
      const newStringMatch = parametersText.match(/<new_string>([\s\S]*?)<\/new_string>/);
      const expectedReplacementsMatch = parametersText.match(/<expected_replacements>(\d+)<\/expected_replacements>/);

      if (filePathMatch && oldStringMatch && newStringMatch) {
        const filePath = filePathMatch[1].trim();
        let oldString = this.decodeHtmlEntities(oldStringMatch[1]);
        let newString = this.decodeHtmlEntities(newStringMatch[1]);
        const expectedReplacements = expectedReplacementsMatch ? parseInt(expectedReplacementsMatch[1]) : 1;

        // 智能处理字符串格式
        oldString = this.normalizeStringForMatching(oldString);
        newString = this.normalizeStringForMatching(newString);

        if (this.options.verbose) {
          console.log(chalk.gray('       ✅ 成功解析标准工具调用格式'));
          console.log(chalk.gray(`       文件路径: ${filePath}`));
          console.log(chalk.gray(`       原文本长度: ${oldString.length} 字符`));
          console.log(chalk.gray(`       新文本长度: ${newString.length} 字符`));
          console.log(chalk.gray(`       期望替换次数: ${expectedReplacements}`));
        }

        return {
          success: true,
          format: 'tool_call',
          filePath,
          oldString,
          newString,
          expectedReplacements,
          aiResponse: fullResponse
        };
      } else {
        return {
          success: false,
          error: '工具调用参数不完整，缺少必要的 file_path、old_string 或 new_string',
          aiResponse: fullResponse
        };
      }
    } catch (parseError) {
      return {
        success: false,
        error: `解析工具调用参数失败: ${parseError.message}`,
        aiResponse: fullResponse
      };
    }
  }

  /**
   * 解析简化格式（向后兼容）
   * @param {Array} match - 正则匹配结果
   * @param {string} fullResponse - 完整响应
   * @returns {Object} 解析结果
   */
  parseSimpleFormat(match, fullResponse) {
    let oldString = this.decodeHtmlEntities(match[1]);
    let newString = this.decodeHtmlEntities(match[2]);

    // 智能处理字符串格式
    oldString = this.normalizeStringForMatching(oldString);
    newString = this.normalizeStringForMatching(newString);

    if (this.options.verbose) {
      console.log(chalk.gray('       ✅ 成功解析简化格式（向后兼容）'));
      console.log(chalk.gray(`       原文本长度: ${oldString.length} 字符`));
      console.log(chalk.gray(`       新文本长度: ${newString.length} 字符`));
    }

    return {
      success: true,
      format: 'simple',
      filePath: null, // 简化格式不包含文件路径
      oldString,
      newString,
      expectedReplacements: 1,
      aiResponse: fullResponse
    };
  }

  /**
   * 解码 HTML 实体
   * @param {string} text - 要解码的文本
   * @returns {string} 解码后的文本
   */
  decodeHtmlEntities(text) {
    const entities = {
      '&lt;': '<',
      '&gt;': '>',
      '&amp;': '&',
      '&quot;': '"',
      '&#39;': "'",
      '&apos;': "'"
    };

    return text.replace(/&[a-zA-Z0-9#]+;/g, (entity) => {
      return entities[entity] || entity;
    });
  }

  /**
   * 标准化字符串以便更好地匹配
   * 处理常见的空格、缩进、换行符问题
   * @param {string} text - 要标准化的文本
   * @returns {string} 标准化后的文本
   */
  normalizeStringForMatching(text) {
    if (!text || typeof text !== 'string') {
      return text;
    }

    // 标准化换行符
    let normalized = text.replace(/\r\n/g, '\n');
    normalized = normalized.replace(/\r/g, '\n');

    // 如果字符串包含多行，保持相对缩进但移除多余的前导空格
    if (normalized.includes('\n')) {
      const lines = normalized.split('\n');

      // 找到最小的非空行缩进
      let minIndent = Infinity;
      for (const line of lines) {
        if (line.trim().length > 0) {
          const indent = line.match(/^(\s*)/)[1].length;
          minIndent = Math.min(minIndent, indent);
        }
      }

      // 如果找到了有效的最小缩进，移除多余的前导空格
      if (minIndent > 0 && minIndent !== Infinity) {
        const adjustedLines = lines.map(line => {
          if (line.trim().length === 0) {
            return ''; // 空行保持为空
          }
          return line.substring(minIndent);
        });
        normalized = adjustedLines.join('\n');
      }

      // 移除开头和结尾的空行
      normalized = normalized.replace(/^\n+/, '').replace(/\n+$/, '');
    } else {
      // 单行字符串只移除首尾空白
      normalized = normalized.trim();
    }

    return normalized;
  }

  /**
   * 截断输出内容
   * @param {string} output - 输出内容
   * @returns {string} 截断后的内容
   */
  truncateOutput(output) {
    if (!output || typeof output !== 'string') {
      return '';
    }

    const maxLength = 8000;
    if (output.length <= maxLength) {
      return output;
    }

    return output.substring(0, maxLength) + '\n\n... (输出已截断)';
  }

  /**
   * 格式化上下文文件
   * @param {Object} contextFiles - 上下文文件
   * @returns {string} 格式化后的内容
   */
  formatContextFiles(contextFiles) {
    if (!contextFiles || Object.keys(contextFiles).length === 0) {
      return '无相关文件';
    }

    return Object.entries(contextFiles)
      .map(([filePath, content]) => `**${filePath}**:\n\`\`\`\n${content}\n\`\`\``)
      .join('\n\n');
  }

  /**
   * 格式化之前的尝试记录
   * @param {Array} previousAttempts - 之前的尝试记录
   * @returns {string} 格式化后的内容
   */
  formatPreviousAttempts(previousAttempts) {
    if (!previousAttempts || previousAttempts.length === 0) {
      return '无之前的尝试记录';
    }

    return previousAttempts
      .map((attempt, index) =>
        `${index + 1}. 第${attempt.attemptNumber}次尝试 - ${attempt.approach}: ${attempt.error}`
      )
      .join('\n');
  }
}

module.exports = StringReplaceTool;
