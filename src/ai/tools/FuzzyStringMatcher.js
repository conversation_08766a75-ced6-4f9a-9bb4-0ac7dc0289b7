/**
 * 模糊字符串匹配器
 * 专门处理代码中的空格、缩进、换行符差异问题
 */
class FuzzyStringMatcher {
  constructor(options = {}) {
    this.options = {
      // 是否忽略前导空格差异
      ignoreLeadingWhitespace: true,
      // 是否忽略尾随空格差异
      ignoreTrailingWhitespace: true,
      // 是否标准化换行符
      normalizeLineEndings: true,
      // 是否忽略空行差异
      ignoreEmptyLines: false,
      // 最大允许的缩进差异（字符数）
      maxIndentDifference: 8,
      // 是否启用详细日志
      verbose: false,
      ...options
    };
  }

  /**
   * 在目标文本中查找模式文本的匹配位置
   * @param {string} targetText - 目标文本（文件内容）
   * @param {string} patternText - 模式文本（要查找的内容）
   * @returns {Object} 匹配结果
   */
  findMatch(targetText, patternText) {
    if (!targetText || !patternText) {
      return { found: false, error: '目标文本或模式文本为空' };
    }

    // 1. 首先尝试精确匹配
    const exactMatch = this.findExactMatch(targetText, patternText);
    if (exactMatch.found) {
      return exactMatch;
    }

    // 2. 尝试标准化后的匹配
    const normalizedMatch = this.findNormalizedMatch(targetText, patternText);
    if (normalizedMatch.found) {
      return normalizedMatch;
    }

    // 3. 尝试模糊匹配（处理缩进差异）
    const fuzzyMatch = this.findFuzzyMatch(targetText, patternText);
    if (fuzzyMatch.found) {
      return fuzzyMatch;
    }

    // 4. 尝试基于语义的匹配（去除所有空格后比较）
    const semanticMatch = this.findSemanticMatch(targetText, patternText);
    if (semanticMatch.found) {
      return semanticMatch;
    }

    return {
      found: false,
      error: '未找到匹配的文本',
      suggestions: this.generateSuggestions(targetText, patternText)
    };
  }

  /**
   * 精确匹配
   */
  findExactMatch(targetText, patternText) {
    const index = targetText.indexOf(patternText);
    if (index !== -1) {
      return {
        found: true,
        method: 'exact',
        index,
        matchedText: patternText,
        originalText: patternText
      };
    }
    return { found: false };
  }

  /**
   * 标准化后匹配
   */
  findNormalizedMatch(targetText, patternText) {
    const normalizedTarget = this.normalizeText(targetText);
    const normalizedPattern = this.normalizeText(patternText);

    const index = normalizedTarget.indexOf(normalizedPattern);
    if (index !== -1) {
      // 找到原始文本中对应的位置
      const originalMatch = this.findOriginalTextFromNormalized(
        targetText, normalizedTarget, index, normalizedPattern.length
      );

      return {
        found: true,
        method: 'normalized',
        index: originalMatch.index,
        matchedText: originalMatch.text,
        originalText: patternText
      };
    }
    return { found: false };
  }

  /**
   * 模糊匹配（处理缩进差异）
   */
  findFuzzyMatch(targetText, patternText) {
    const targetLines = targetText.split('\n');
    const patternLines = patternText.split('\n');

    // 寻找第一行的匹配
    const firstPatternLine = this.normalizeText(patternLines[0]);
    
    for (let i = 0; i < targetLines.length; i++) {
      const normalizedTargetLine = this.normalizeText(targetLines[i]);
      
      if (normalizedTargetLine === firstPatternLine) {
        // 检查后续行是否也匹配
        const matchResult = this.checkMultiLineMatch(
          targetLines, patternLines, i
        );
        
        if (matchResult.matches) {
          return {
            found: true,
            method: 'fuzzy',
            index: this.getIndexFromLineNumber(targetText, i),
            matchedText: matchResult.matchedText,
            originalText: patternText,
            lineStart: i,
            lineEnd: i + patternLines.length - 1
          };
        }
      }
    }

    return { found: false };
  }

  /**
   * 语义匹配（去除所有空格）
   */
  findSemanticMatch(targetText, patternText) {
    const semanticTarget = this.removeAllWhitespace(targetText);
    const semanticPattern = this.removeAllWhitespace(patternText);

    const index = semanticTarget.indexOf(semanticPattern);
    if (index !== -1) {
      // 这种情况下很难精确定位原始位置，返回近似结果
      return {
        found: true,
        method: 'semantic',
        index: -1, // 无法精确定位
        matchedText: null,
        originalText: patternText,
        warning: '语义匹配成功，但无法精确定位原始文本位置'
      };
    }

    return { found: false };
  }

  /**
   * 检查多行匹配
   */
  checkMultiLineMatch(targetLines, patternLines, startIndex) {
    if (startIndex + patternLines.length > targetLines.length) {
      return { matches: false };
    }

    const matchedLines = [];
    
    for (let i = 0; i < patternLines.length; i++) {
      const targetLine = targetLines[startIndex + i];
      const patternLine = patternLines[i];
      
      const normalizedTarget = this.normalizeText(targetLine);
      const normalizedPattern = this.normalizeText(patternLine);
      
      if (normalizedTarget !== normalizedPattern) {
        return { matches: false };
      }
      
      matchedLines.push(targetLine);
    }

    return {
      matches: true,
      matchedText: matchedLines.join('\n')
    };
  }

  /**
   * 标准化文本
   */
  normalizeText(text) {
    if (!text) return '';

    let normalized = text;

    // 标准化换行符
    if (this.options.normalizeLineEndings) {
      normalized = normalized.replace(/\r\n/g, '\n').replace(/\r/g, '\n');
    }

    // 处理前导空格
    if (this.options.ignoreLeadingWhitespace) {
      normalized = normalized.replace(/^\s+/gm, '');
    }

    // 处理尾随空格
    if (this.options.ignoreTrailingWhitespace) {
      normalized = normalized.replace(/\s+$/gm, '');
    }

    // 处理空行
    if (this.options.ignoreEmptyLines) {
      normalized = normalized.replace(/^\s*\n/gm, '');
    }

    return normalized;
  }

  /**
   * 移除所有空白字符
   */
  removeAllWhitespace(text) {
    return text.replace(/\s/g, '');
  }

  /**
   * 从行号获取字符索引
   */
  getIndexFromLineNumber(text, lineNumber) {
    const lines = text.split('\n');
    let index = 0;
    
    for (let i = 0; i < lineNumber && i < lines.length; i++) {
      index += lines[i].length + 1; // +1 for newline
    }
    
    return index;
  }

  /**
   * 从标准化文本中找到原始文本
   */
  findOriginalTextFromNormalized(originalText, normalizedText, normalizedIndex, normalizedLength) {
    // 获取标准化文本中的目标片段
    const normalizedTarget = normalizedText.substring(normalizedIndex, normalizedIndex + normalizedLength);
    
    // 首先尝试直接在原始文本中查找完全匹配
    const directMatch = originalText.indexOf(normalizedTarget);
    if (directMatch !== -1) {
      return {
        index: directMatch,
        text: normalizedTarget
      };
    }
    
    // 如果直接匹配失败，使用精确的字符对字符映射
    return this.buildPreciseMapping(originalText, normalizedText, normalizedIndex, normalizedLength);
  }

  /**
   * 构建精确的位置映射
   */
  buildPreciseMapping(originalText, normalizedText, normalizedIndex, normalizedLength) {
    // 建立精确的字符位置映射表
    const mapping = [];
    let originalPos = 0;
    let normalizedPos = 0;
    
    while (originalPos < originalText.length && normalizedPos < normalizedText.length) {
      const originalChar = originalText[originalPos];
      const normalizedChar = normalizedText[normalizedPos];
      
      // 跳过原始文本中的前导空白字符
      if (/^\s+/.test(originalText.substring(originalPos)) && 
          !/^\s/.test(normalizedText.substring(normalizedPos))) {
        // 跳过原始文本中被标准化移除的空白
        originalPos++;
        continue;
      }
      
      // 建立字符映射关系
      mapping.push({
        originalIndex: originalPos,
        normalizedIndex: normalizedPos
      });
      
      if (originalChar === normalizedChar) {
        // 字符完全匹配，同时前进
        originalPos++;
        normalizedPos++;
      } else if (/\s/.test(originalChar) && /\s/.test(normalizedChar)) {
        // 都是空白字符，视为匹配
        originalPos++;
        normalizedPos++;
      } else {
        // 字符不匹配，但都向前推进
        originalPos++;
        normalizedPos++;
      }
    }
    
    // 查找目标范围在原始文本中的对应位置
    const startMapping = mapping.find(m => m.normalizedIndex >= normalizedIndex);
    const endMapping = mapping.find(m => m.normalizedIndex >= normalizedIndex + normalizedLength);
    
    if (startMapping) {
      const startIndex = startMapping.originalIndex;
      const endIndex = endMapping ? endMapping.originalIndex : 
        Math.min(startIndex + normalizedLength, originalText.length);
      
      return {
        index: startIndex,
        text: originalText.substring(startIndex, endIndex)
      };
    }
    
    // 回退方案：使用保守估算
    const safeIndex = Math.min(normalizedIndex, originalText.length - normalizedLength);
    return {
      index: Math.max(0, safeIndex),
      text: originalText.substring(safeIndex, safeIndex + normalizedLength)
    };
  }

  /**
   * 生成修复建议
   */
  generateSuggestions(targetText, patternText) {
    const suggestions = [];

    // 检查是否是缩进问题
    const patternWithoutIndent = patternText.replace(/^\s+/gm, '');
    if (targetText.includes(patternWithoutIndent)) {
      suggestions.push({
        type: 'indentation',
        message: '检测到缩进差异，尝试移除模式文本的前导空格',
        suggestedPattern: patternWithoutIndent
      });
    }

    // 检查是否是换行符问题
    const normalizedPattern = patternText.replace(/\r\n/g, '\n').replace(/\r/g, '\n');
    if (targetText.includes(normalizedPattern)) {
      suggestions.push({
        type: 'line_endings',
        message: '检测到换行符差异，尝试标准化换行符',
        suggestedPattern: normalizedPattern
      });
    }

    // 检查是否是空格数量问题
    const compactPattern = patternText.replace(/\s+/g, ' ');
    if (targetText.includes(compactPattern)) {
      suggestions.push({
        type: 'whitespace',
        message: '检测到空格数量差异，尝试标准化空格',
        suggestedPattern: compactPattern
      });
    }

    return suggestions;
  }

  /**
   * 执行智能替换
   */
  smartReplace(targetText, patternText, replacementText) {
    // 输入验证
    if (!targetText || !patternText || replacementText === undefined) {
      return {
        success: false,
        error: '输入参数无效：targetText、patternText 或 replacementText 为空'
      };
    }

    const matchResult = this.findMatch(targetText, patternText);
    
    if (!matchResult.found) {
      return {
        success: false,
        error: matchResult.error,
        suggestions: matchResult.suggestions
      };
    }

    let result;
    
    try {
      if (matchResult.method === 'exact') {
        // 最安全的精确替换
        result = targetText.replace(patternText, replacementText);
      } else if (matchResult.method === 'normalized') {
        // 使用位置信息进行精确替换
        if (matchResult.index < 0 || !matchResult.matchedText) {
          throw new Error('标准化匹配缺少必要的位置信息');
        }
        
        result = targetText.substring(0, matchResult.index) + 
                 replacementText + 
                 targetText.substring(matchResult.index + matchResult.matchedText.length);
      } else if (matchResult.method === 'fuzzy') {
        // 谨慎的行级替换
        result = this.performSafeFuzzyReplace(targetText, matchResult, replacementText);
      } else {
        return {
          success: false,
          error: '不支持的匹配方法: ' + matchResult.method,
          method: matchResult.method
        };
      }

      // 替换后验证
      if (!result || result.length === 0) {
        return {
          success: false,
          error: '替换结果为空，可能存在算法错误'
        };
      }

      // 验证替换是否真的发生了
      if (result === targetText) {
        return {
          success: false,
          error: '替换未生效，原文本未发生变化'
        };
      }

      return {
        success: true,
        result,
        method: matchResult.method,
        originalMatch: matchResult
      };
    } catch (error) {
      return {
        success: false,
        error: '替换过程中发生错误: ' + error.message,
        method: matchResult.method
      };
    }
  }

  /**
   * 安全的模糊替换
   */
  performSafeFuzzyReplace(targetText, matchResult, replacementText) {
    if (matchResult.lineStart === undefined || matchResult.lineEnd === undefined) {
      throw new Error('模糊匹配缺少行号信息');
    }

    const lines = targetText.split('\n');
    const replacementLines = replacementText.split('\n');
    
    // 验证行号范围
    if (matchResult.lineStart < 0 || matchResult.lineEnd >= lines.length || 
        matchResult.lineStart > matchResult.lineEnd) {
      throw new Error(`无效的行号范围: ${matchResult.lineStart}-${matchResult.lineEnd}`);
    }

    // 执行行替换
    const newLines = [
      ...lines.slice(0, matchResult.lineStart),
      ...replacementLines,
      ...lines.slice(matchResult.lineEnd + 1)
    ];
    
    return newLines.join('\n');
  }
}

module.exports = FuzzyStringMatcher;
