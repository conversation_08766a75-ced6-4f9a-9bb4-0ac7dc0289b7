const crypto = require('crypto')
const axios = require('axios')
require('dotenv').config()

class GalaxyAiService {
	constructor (options = {}) {
		this.baseURL = options.baseURL || process.env.GALAXY_BASE_URL || 'http://copilot.prodgpu.chinastock.com.cn'
		this.systemId = options.systemId || process.env.GALAXY_SYSTEM_ID
		this.systemSecret = options.systemSecret || process.env.GALAXY_SYSTEM_SECRET
		this.account = options.account || process.env.GALAXY_ACCOUNT
		this.appId = options.appId || process.env.GALAXY_APP_ID

		if (!this.systemId || !this.systemSecret || !this.account || !this.appId) {
			throw new Error('GALAXY_SYSTEM_ID, GALAXY_SYSTEM_SECRET, GALAXY_ACCOUNT, and GALAXY_APP_ID are required')
		}

		this.accessToken = null
		this.tokenExpiry = null
	}

	/**
	 *
	 * HOST Server: http://copilot.prodgpu.chinastock.com.cn/
	 *
	 * 系统授权：
	 *
	 * - URL： POST api/system_token
	 * - 请求方式：application/json
	 * - 接口描述：通过 api/system_token 获取的access_token 有效时间为 7 天。（System-Id  和 System-secret 需要联系系统管理员获取）
	 * - 请求参数说明:
	 *
	 * 请求示例
	 * ```json
	 * {
	 *   "user-nonce":"*************",
	 *   "System-Token":"3226fb8fddf3e9db3aaf3f913a5ba0ed",
	 *   "System-Id":"oa-system"
	 *   "account":"***********"
	 * }
	 * ```
	 * - 签名说明:
	 * System-Token 加密方式 ：System-Id  + \t + System-secret   + \t + user-nonce 组成字符串后用MD5加密
	 * - 示例:
	 * system_id = 'oa-system'
	 * system_secret = 'Aa222@b2#Cc3$Dd4%F6^Gh7&8*K90Mn?'
	 * user-nonce为： '*************'
	 * 签名后的 System-Token： '3226fb8fddf3e9db3aaf3f913a5ba0ed'
	 *
	 * ```python
	 * import hashlib
	 * import time
	 *
	 * def md5_encryption(data):
	 *     md5 = hashlib.md5()
	 *     md5.update(data.encode("utf-8"))
	 *     return md5.hexdigest()
	 *
	 *
	 * system_id = 'system_oa'
	 * system_secret = 'Aa222@b2#Cc3$Dd4%F6^Gh7&8*K90Mn?'
	 * user_nonce = str(int(time.time() * 1000))
	 * print("时间戳为：", user_nonce)
	 * print("System-Token：", md5_encryption(f"{system_id}\t{system_secret}\t{user_nonce}"))
	 * ```
	 */
	async getSystemToken () {
		const userNonce = Date.now().toString()
		const signatureString = `${this.systemId}\t${this.systemSecret}\t${userNonce}`
		const systemToken = crypto.createHash('md5').update(signatureString, 'utf8').digest('hex')

		const requestData = {
			'user-nonce': userNonce,
			'System-Token': systemToken,
			'System-Id': this.systemId,
			account: this.account
		}

		try {
			const response = await axios.post(`${this.baseURL}/api/system_token`, requestData, {
				headers: {
					'Content-Type': 'application/json'
				}
			})

			if (response.data && response.data.access_token) {
				this.accessToken = response.data.access_token
				// Token valid for 7 days
				this.tokenExpiry = Date.now() + (7 * 24 * 60 * 60 * 1000)
				return this.accessToken
			} else {
				throw new Error('Failed to get access token from response')
			}
		} catch (error) {
			throw new Error(`Failed to get system token: ${error.message}`)
		}
	}

	/**
	 * Check if token is valid and refresh if needed
	 */
	async ensureValidToken () {
		if (!this.accessToken || !this.tokenExpiry || Date.now() >= this.tokenExpiry) {
			await this.getSystemToken()
		}
	}

	/**
	 * http://copilot.prodgpu.chinastock.com.cn/api/app/xchat
	 *
	 * 调用助手问答接口
	 * - POST api/app/xchat
	 * - 数据传输格式 application/json
	 * 示例：
	 *
	 * ```json
	 * JSON
	 *  {
	 *   "messages": [
	 *     {
	 *       "role": "user",
	 *       "content": "你好呀"
	 *     }
	 *   ],
	 *   "conversation_id": "chat-fzBsPshg2bizKg2dz7kFkE",
	 *   "stream": true,
	 *   "app_id": "app-LQMX9oFMy4TJ36eGUj2sBK",
	 *   "tag": [
	 *     "user-1008611"
	 *   ]
	 * }
	 * ```
	 *
	 * request 参数说明: 需要拿到第一步的模型平台 的 access_token
	 * response 区别于 OpenAI 的 response，是：`$.delta`
	 */
	async chat (messages, conversationId, appId, tags = []) {
		await this.ensureValidToken()

		const requestData = {
			messages,
			conversation_id: conversationId,
			stream: true,
			app_id: appId || this.appId,
			tag: tags
		}

		try {
			const response = await axios.post(`${this.baseURL}/api/app/xchat`, requestData, {
				headers: {
					'Content-Type': 'application/json',
					'Authorization': `Bearer ${this.accessToken}`
				},
				responseType: 'stream'
			})

			return response
		} catch (error) {
			throw new Error(`Failed to chat: ${error}`)
		}
	}

	/**
	 * Create a Galaxy parser similar to Dify parser but for Galaxy API response format
	 * Galaxy response format uses $.delta instead of OpenAI format
	 */
	async createGalaxyParser (response) {
		const reader = response.data?.getReader ? response.data.getReader() : response.data

		if (!reader) throw new Error('No reader available')

		let buffer = ''

		return new Promise((resolve, reject) => {
			const chunks = []
			reader.on('data', (chunk) => {
				const dataStr = chunk.toString()
				buffer += dataStr

				const lines = buffer.split('\n')
				buffer = lines.pop() || ''

				for (const line of lines) {
					const message = line.replace(/^data: /, '')
					if (message === '[DONE]') {
						resolve(chunks)
						return
					} else if (message !== 'event: ping' && message.trim()) {
						try {
							const parsed = JSON.parse(message)
							if (parsed.delta) {
								chunks.push(parsed.delta)
							}
						} catch (error) {
							// Ignore parse errors
						}
					}
				}
			})

			reader.on('end', () => {
				resolve(chunks)
			})
			reader.on('error', reject)
		})
	}

	/**
	 * Convert Galaxy response to AI.js compatible format
	 * This method makes Galaxy API compatible with AI.js SDK generate-text API
	 */
	async generateText (options) {
		const { prompt, messages, maxTokens, temperature, ...otherOptions } = options

		let chatMessages = messages
		if (prompt && !messages) {
			chatMessages = [{ role: 'user', content: prompt }]
		}

		const conversationId = otherOptions.conversationId || `chat-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`

		const response = await this.chat(chatMessages, conversationId, otherOptions.appId, otherOptions.tags)
		const chunks = await this.createGalaxyParser(response)

		let text = ''
		if (Array.isArray(chunks)) {
			text = chunks.join('')
		} else {
			// Handle async generator
			for await (const chunk of chunks) {
				text += chunk || ''
			}
		}

		return {
			text: text.trim(),
			finishReason: 'stop',
			usage: {
				promptTokens: 0, // Galaxy API doesn't provide token usage
				completionTokens: 0,
				totalTokens: 0
			}
		}
	}
}

module.exports = { GalaxyAiService }
