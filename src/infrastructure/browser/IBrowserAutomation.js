/**
 * IBrowserAutomation - 浏览器自动化抽象接口
 * 
 * 提供统一的浏览器自动化接口，支持 Puppeteer 和 Playwright 两种实现
 * 这样可以在两种工具之间轻松切换，而不需要修改业务代码
 */

/**
 * 浏览器启动选项接口
 */
class BrowserLaunchOptions {
  constructor(options = {}) {
    this.headless = options.headless !== false; // 默认为 true
    this.args = options.args || [];
    this.executablePath = options.executablePath || null;
    this.timeout = options.timeout || 30000;
    this.devtools = options.devtools || false;
    this.slowMo = options.slowMo || 0;
  }
}

/**
 * 页面导航选项接口
 */
class NavigationOptions {
  constructor(options = {}) {
    this.waitUntil = options.waitUntil || 'domcontentloaded';
    this.timeout = options.timeout || 30000;
  }
}

/**
 * 元素等待选项接口
 */
class WaitOptions {
  constructor(options = {}) {
    this.visible = options.visible !== false;
    this.timeout = options.timeout || 30000;
  }
}

/**
 * 截图选项接口
 */
class ScreenshotOptions {
  constructor(options = {}) {
    this.path = options.path || null;
    this.fullPage = options.fullPage !== false;
    this.type = options.type || 'png';
    // 只有JPEG格式才支持quality参数
    if (this.type === 'jpeg' || this.type === 'jpg') {
      this.quality = options.quality || 90;
    }
  }
}

/**
 * 浏览器自动化抽象接口
 */
class IBrowserAutomation {
  /**
   * 启动浏览器
   * @param {BrowserLaunchOptions} options 启动选项
   * @returns {Promise<IBrowser>} 浏览器实例
   */
  async launch(options = {}) {
    throw new Error('Method launch() must be implemented');
  }

  /**
   * 检测可用的浏览器
   * @returns {Promise<Array>} 可用浏览器列表
   */
  async detectBrowsers() {
    throw new Error('Method detectBrowsers() must be implemented');
  }

  /**
   * 下载浏览器
   * @returns {Promise<boolean>} 下载是否成功
   */
  async downloadBrowser() {
    throw new Error('Method downloadBrowser() must be implemented');
  }

  /**
   * 获取浏览器类型名称
   * @returns {string} 浏览器类型 ('puppeteer' | 'playwright')
   */
  getBrowserType() {
    throw new Error('Method getBrowserType() must be implemented');
  }
}

/**
 * 浏览器实例抽象接口
 */
class IBrowser {
  /**
   * 创建新页面
   * @returns {Promise<IPage>} 页面实例
   */
  async newPage() {
    throw new Error('Method newPage() must be implemented');
  }

  /**
   * 获取所有页面
   * @returns {Promise<Array<IPage>>} 页面列表
   */
  async pages() {
    throw new Error('Method pages() must be implemented');
  }

  /**
   * 关闭浏览器
   * @returns {Promise<void>}
   */
  async close() {
    throw new Error('Method close() must be implemented');
  }

  /**
   * 获取浏览器版本
   * @returns {Promise<string>} 版本信息
   */
  async version() {
    throw new Error('Method version() must be implemented');
  }
}

/**
 * 页面实例抽象接口
 */
class IPage {
  /**
   * 导航到指定URL
   * @param {string} url 目标URL
   * @param {NavigationOptions} options 导航选项
   * @returns {Promise<IResponse>} 响应对象
   */
  async goto(url, options = {}) {
    throw new Error('Method goto() must be implemented');
  }

  /**
   * 获取当前页面URL
   * @returns {string} 当前URL
   */
  url() {
    throw new Error('Method url() must be implemented');
  }

  /**
   * 设置视口大小
   * @param {Object} viewport 视口配置 {width, height}
   * @returns {Promise<void>}
   */
  async setViewport(viewport) {
    throw new Error('Method setViewport() must be implemented');
  }

  /**
   * 页面截图
   * @param {ScreenshotOptions} options 截图选项
   * @returns {Promise<Buffer>} 截图数据
   */
  async screenshot(options = {}) {
    throw new Error('Method screenshot() must be implemented');
  }

  /**
   * 在页面中执行JavaScript
   * @param {Function|string} pageFunction 要执行的函数
   * @param {...any} args 函数参数
   * @returns {Promise<any>} 执行结果
   */
  async evaluate(pageFunction, ...args) {
    throw new Error('Method evaluate() must be implemented');
  }

  /**
   * 等待选择器出现
   * @param {string} selector CSS选择器
   * @param {WaitOptions} options 等待选项
   * @returns {Promise<IElementHandle>} 元素句柄
   */
  async waitForSelector(selector, options = {}) {
    throw new Error('Method waitForSelector() must be implemented');
  }

  /**
   * 等待函数返回真值
   * @param {Function|string} pageFunction 要执行的函数
   * @param {Object} options 等待选项
   * @param {...any} args 函数参数
   * @returns {Promise<any>} 函数返回值
   */
  async waitForFunction(pageFunction, options = {}, ...args) {
    throw new Error('Method waitForFunction() must be implemented');
  }

  /**
   * 查找元素
   * @param {string} selector CSS选择器
   * @returns {Promise<IElementHandle|null>} 元素句柄
   */
  async $(selector) {
    throw new Error('Method $() must be implemented');
  }

  /**
   * 查找多个元素
   * @param {string} selector CSS选择器
   * @returns {Promise<Array<IElementHandle>>} 元素句柄数组
   */
  async $$(selector) {
    throw new Error('Method $$() must be implemented');
  }

  /**
   * 设置默认超时时间
   * @param {number} timeout 超时时间（毫秒）
   * @returns {void}
   */
  setDefaultTimeout(timeout) {
    throw new Error('Method setDefaultTimeout() must be implemented');
  }

  /**
   * 设置默认导航超时时间
   * @param {number} timeout 超时时间（毫秒）
   * @returns {void}
   */
  setDefaultNavigationTimeout(timeout) {
    throw new Error('Method setDefaultNavigationTimeout() must be implemented');
  }

  /**
   * 监听页面事件
   * @param {string} event 事件名称
   * @param {Function} handler 事件处理函数
   * @returns {void}
   */
  on(event, handler) {
    throw new Error('Method on() must be implemented');
  }

  /**
   * 移除事件监听器
   * @param {string} event 事件名称
   * @param {Function} handler 事件处理函数
   * @returns {void}
   */
  off(event, handler) {
    throw new Error('Method off() must be implemented');
  }

  /**
   * 关闭页面
   * @returns {Promise<void>}
   */
  async close() {
    throw new Error('Method close() must be implemented');
  }
}

/**
 * 元素句柄抽象接口
 */
class IElementHandle {
  /**
   * 点击元素
   * @param {Object} options 点击选项
   * @returns {Promise<void>}
   */
  async click(options = {}) {
    throw new Error('Method click() must be implemented');
  }

  /**
   * 输入文本
   * @param {string} text 要输入的文本
   * @param {Object} options 输入选项
   * @returns {Promise<void>}
   */
  async type(text, options = {}) {
    throw new Error('Method type() must be implemented');
  }

  /**
   * 获取元素文本内容
   * @returns {Promise<string>} 文本内容
   */
  async textContent() {
    throw new Error('Method textContent() must be implemented');
  }

  /**
   * 获取元素属性值
   * @param {string} name 属性名
   * @returns {Promise<string|null>} 属性值
   */
  async getAttribute(name) {
    throw new Error('Method getAttribute() must be implemented');
  }
}

/**
 * 响应对象抽象接口
 */
class IResponse {
  /**
   * 获取响应状态码
   * @returns {number} 状态码
   */
  status() {
    throw new Error('Method status() must be implemented');
  }

  /**
   * 获取响应状态文本
   * @returns {string} 状态文本
   */
  statusText() {
    throw new Error('Method statusText() must be implemented');
  }

  /**
   * 检查响应是否成功
   * @returns {boolean} 是否成功
   */
  ok() {
    throw new Error('Method ok() must be implemented');
  }

  /**
   * 获取响应URL
   * @returns {string} 响应URL
   */
  url() {
    throw new Error('Method url() must be implemented');
  }
}

module.exports = {
  IBrowserAutomation,
  IBrowser,
  IPage,
  IElementHandle,
  IResponse,
  BrowserLaunchOptions,
  NavigationOptions,
  WaitOptions,
  ScreenshotOptions
};
