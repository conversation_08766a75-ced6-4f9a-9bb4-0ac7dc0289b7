/**
 * 浏览器自动化模块入口文件
 * 
 * 提供统一的浏览器自动化接口，支持 Puppeteer 和 Playwright
 * 使用工厂模式来创建不同类型的浏览器自动化实例
 */

// 导出抽象接口
const {
  IBrowserAutomation,
  IBrowser,
  IPage,
  IElementHandle,
  IResponse,
  BrowserLaunchOptions,
  NavigationOptions,
  WaitOptions,
  ScreenshotOptions
} = require('./IBrowserAutomation');

// 导出适配器实现
const {
  PuppeteerAdapter,
  PuppeteerBrowserAdapter,
  PuppeteerPageAdapter,
  PuppeteerElementAdapter,
  PuppeteerResponseAdapter
} = require('./PuppeteerAdapter');

const {
  PlaywrightAdapter,
  PlaywrightBrowserAdapter,
  PlaywrightPageAdapter,
  PlaywrightElementAdapter,
  PlaywrightResponseAdapter
} = require('./PlaywrightAdapter');

// 导出工厂类
const { BrowserFactory, browserFactory } = require('./BrowserFactory');

/**
 * 便捷函数：创建浏览器自动化实例
 * @param {string} type 浏览器类型
 * @returns {IBrowserAutomation} 浏览器自动化实例
 */
function createBrowserAutomation(type = 'puppeteer') {
  return browserFactory.createBrowserAutomation(type);
}

/**
 * 便捷函数：自动选择并创建最佳的浏览器自动化实例
 * @param {Object} options 配置选项
 * @returns {Promise<IBrowserAutomation>} 浏览器自动化实例
 */
async function createBestBrowserAutomation(options = {}) {
  return await browserFactory.createConfiguredBrowserAutomation(options);
}

/**
 * 便捷函数：检测可用的浏览器
 * @returns {Promise<Array>} 可用浏览器列表
 */
async function detectAvailableBrowsers() {
  return await browserFactory.detectAvailableBrowsers();
}

module.exports = {
  // 抽象接口
  IBrowserAutomation,
  IBrowser,
  IPage,
  IElementHandle,
  IResponse,
  BrowserLaunchOptions,
  NavigationOptions,
  WaitOptions,
  ScreenshotOptions,

  // 适配器实现
  PuppeteerAdapter,
  PuppeteerBrowserAdapter,
  PuppeteerPageAdapter,
  PuppeteerElementAdapter,
  PuppeteerResponseAdapter,

  PlaywrightAdapter,
  PlaywrightBrowserAdapter,
  PlaywrightPageAdapter,
  PlaywrightElementAdapter,
  PlaywrightResponseAdapter,

  // 工厂类
  BrowserFactory,
  browserFactory,

  // 便捷函数
  createBrowserAutomation,
  createBestBrowserAutomation,
  detectAvailableBrowsers
};
