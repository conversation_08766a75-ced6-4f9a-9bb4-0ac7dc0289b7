const puppeteer = require('puppeteer');
const {
  IBrowserAutomation,
  IBrowser,
  IPage,
  IElementHandle,
  IResponse,
  BrowserLaunchOptions,
  NavigationOptions,
  WaitOptions,
  ScreenshotOptions
} = require('./IBrowserAutomation');

/**
 * Puppeteer 适配器实现
 */
class PuppeteerAdapter extends IBrowserAutomation {
  constructor() {
    super();
    this.puppeteer = puppeteer;
  }

  async launch(options = {}) {
    const launchOptions = new BrowserLaunchOptions(options);
    
    const puppeteerOptions = {
      headless: launchOptions.headless,
      args: launchOptions.args,
      executablePath: launchOptions.executablePath,
      devtools: launchOptions.devtools,
      slowMo: launchOptions.slowMo
    };

    // 移除 null 值
    Object.keys(puppeteerOptions).forEach(key => {
      if (puppeteerOptions[key] === null) {
        delete puppeteerOptions[key];
      }
    });

    const browser = await this.puppeteer.launch(puppeteerOptions);
    return new PuppeteerBrowserAdapter(browser);
  }

  async detectBrowsers() {
    try {
      const browserFetcher = this.puppeteer.createBrowserFetcher();
      const localRevisions = await browserFetcher.localRevisions();
      
      const browsers = [];
      for (const revision of localRevisions) {
        const revisionInfo = browserFetcher.revisionInfo(revision);
        browsers.push({
          name: 'Chrome (Puppeteer)',
          type: 'puppeteer',
          executablePath: revisionInfo.executablePath,
          version: revision,
          available: true
        });
      }
      
      return browsers;
    } catch (error) {
      return [];
    }
  }

  async downloadBrowser() {
    try {
      const browserFetcher = this.puppeteer.createBrowserFetcher();
      await browserFetcher.download(this.puppeteer.PUPPETEER_REVISIONS.chromium);
      return true;
    } catch (error) {
      return false;
    }
  }

  getBrowserType() {
    return 'puppeteer';
  }
}

/**
 * Puppeteer 浏览器适配器
 */
class PuppeteerBrowserAdapter extends IBrowser {
  constructor(browser) {
    super();
    this.browser = browser;
  }

  async newPage() {
    const page = await this.browser.newPage();
    return new PuppeteerPageAdapter(page);
  }

  async pages() {
    const pages = await this.browser.pages();
    return pages.map(page => new PuppeteerPageAdapter(page));
  }

  async close() {
    return await this.browser.close();
  }

  async version() {
    return await this.browser.version();
  }
}

/**
 * Puppeteer 页面适配器
 */
class PuppeteerPageAdapter extends IPage {
  constructor(page) {
    super();
    this.page = page;
  }

  async goto(url, options = {}) {
    const navOptions = new NavigationOptions(options);
    const response = await this.page.goto(url, {
      waitUntil: navOptions.waitUntil,
      timeout: navOptions.timeout
    });
    return response ? new PuppeteerResponseAdapter(response) : null;
  }

  url() {
    return this.page.url();
  }

  async setViewport(viewport) {
    return await this.page.setViewport(viewport);
  }

  async screenshot(options = {}) {
    const screenshotOptions = new ScreenshotOptions(options);
    const screenshotConfig = {
      path: screenshotOptions.path,
      fullPage: screenshotOptions.fullPage,
      type: screenshotOptions.type
    };

    // 只有JPEG格式才添加quality参数
    if (screenshotOptions.quality !== undefined) {
      screenshotConfig.quality = screenshotOptions.quality;
    }

    return await this.page.screenshot(screenshotConfig);
  }

  async evaluate(pageFunction, ...args) {
    return await this.page.evaluate(pageFunction, ...args);
  }

  async waitForSelector(selector, options = {}) {
    const waitOptions = new WaitOptions(options);
    const element = await this.page.waitForSelector(selector, {
      visible: waitOptions.visible,
      timeout: waitOptions.timeout
    });
    return element ? new PuppeteerElementAdapter(element) : null;
  }

  async waitForFunction(pageFunction, options = {}, ...args) {
    return await this.page.waitForFunction(pageFunction, options, ...args);
  }

  async $(selector) {
    const element = await this.page.$(selector);
    return element ? new PuppeteerElementAdapter(element) : null;
  }

  async $$(selector) {
    const elements = await this.page.$$(selector);
    return elements.map(element => new PuppeteerElementAdapter(element));
  }

  setDefaultTimeout(timeout) {
    this.page.setDefaultTimeout(timeout);
  }

  setDefaultNavigationTimeout(timeout) {
    this.page.setDefaultNavigationTimeout(timeout);
  }

  on(event, handler) {
    this.page.on(event, handler);
  }

  off(event, handler) {
    this.page.off(event, handler);
  }

  async close() {
    return await this.page.close();
  }
}

/**
 * Puppeteer 元素适配器
 */
class PuppeteerElementAdapter extends IElementHandle {
  constructor(element) {
    super();
    this.element = element;
  }

  async click(options = {}) {
    return await this.element.click(options);
  }

  async type(text, options = {}) {
    return await this.element.type(text, options);
  }

  async textContent() {
    return await this.element.evaluate(el => el.textContent);
  }

  async getAttribute(name) {
    return await this.element.evaluate((el, attrName) => el.getAttribute(attrName), name);
  }
}

/**
 * Puppeteer 响应适配器
 */
class PuppeteerResponseAdapter extends IResponse {
  constructor(response) {
    super();
    this.response = response;
  }

  status() {
    return this.response.status();
  }

  statusText() {
    return this.response.statusText();
  }

  ok() {
    return this.response.ok();
  }

  url() {
    return this.response.url();
  }
}

module.exports = {
  PuppeteerAdapter,
  PuppeteerBrowserAdapter,
  PuppeteerPageAdapter,
  PuppeteerElementAdapter,
  PuppeteerResponseAdapter
};
