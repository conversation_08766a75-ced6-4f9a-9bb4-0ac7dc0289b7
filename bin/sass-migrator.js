#!/usr/bin/env node

require('dotenv').config()

const { Command } = require('commander')
const chalk = require('chalk')
const path = require('path')
const fs = require('fs-extra')
const SassMigrator = require('../src/app/SassMigrator')
const program = new Command()

program
	.name('sass-migrator')
	.description('Element UI 到 Element Plus 的 SASS 迁移工具')
	.version('1.0.0')

program
	.command('migrate <project-path>')
	.description('🔄 转换 Element Plus 的 Sass 变量')
	.option('--verbose', '显示详细信息')
	.option('--quiet', '静默模式，仅显示关键信息')
	.action(async (projectPath, options) => {
		try {
			const resolvedProjectPath = path.resolve(projectPath)

			if (!await fs.pathExists(resolvedProjectPath)) {
				throw new Error(`项目路径不存在: ${resolvedProjectPath}`)
			}

			console.log(chalk.blue('🚀 开始 SASS 代码转换...'))
			const migrator = new SassMigrator(resolvedProjectPath, {
				isOutputMode: false,
				srcDir: 'src',
				outputSrcDir: 'src',
				verbose: options.verbose ? options.verbose : false
			})

			const result = await migrator.migrate()
			if (result.failedFiles && result.failedFiles.length > 0) {
				console.log(chalk.yellow(`\n⚠️  发现 ${result.failedFiles.length} 个转换失败的文件，可能需要手动修复`))
			}
		} catch (error) {
			console.error(chalk.red('\n❌ 迁移失败:'), error.message)
			if (process.env.DEBUG) {
				console.error(error.stack)
			}
			process.exit(1)
		}
	})

process.on('uncaughtException', (error) => {
	console.error(chalk.red('❌ 未捕获的异常:'), error.message)
	if (process.env.DEBUG) {
		console.error(error.stack)
	}
	process.exit(1)
})

process.on('unhandledRejection', (reason, promise) => {
	console.error(chalk.red('❌ 未处理的 Promise 拒绝:'), reason)
	if (process.env.DEBUG) {
		console.error(promise)
	}
	process.exit(1)
})

program.parse()

if (!process.argv.slice(2).length) {
	program.outputHelp()
}
