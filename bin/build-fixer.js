#!/usr/bin/env node

require('dotenv').config();

const { Command } = require('commander');
const chalk = require('chalk');
const path = require('path');
const fs = require('fs-extra');
const ora = require('ora');
const BuildFixer = require('../src/app/BuildFixer');
const ConfigLoader = require('../src/domain/build-fix/ConfigLoader');

const program = new Command();

program
	.name('build-fixer')
	.description('Vue 项目构建错误自动修复工具')
	.version('1.0.0');

program
	.command('fix [project-path]')
	.description('🔧 修复项目构建错误')
	.option('-c, --build-command <cmd>', '构建命令')
	.option('-d, --dev-command <cmd>', 'Dev 命令')
	.option('-i, --install-command <cmd>', '安装命令')
	.option('-m, --max-attempts <num>', '最大修复尝试次数')
	.option('--mode <mode>', '运行模式: dev (30s检测) 或 build (完整构建)', 'build')
	.option('--dev-timeout <ms>', 'Dev 模式超时时间(毫秒)')
	.option('--no-legacy-peer-deps', '禁用 --legacy-peer-deps 安装选项')
	.option('--skip-install', '跳过依赖安装')
	.option('--skip-ai', '跳过 AI 修复步骤')
	.option('--dry-run', '预览模式，不实际修改文件')
	.option('--interactive', '交互式修复模式，让用户选择要修复的错误')
	.option('--explain', '解释每个错误的原因和修复方案')
	.option('--enable-runtime-repair', '启用运行时错误修复')
	.option('--runtime-timeout <ms>', '运行时监控超时时间(毫秒)', '60000')
	.option('--no-runtime-auto-fix', '禁用运行时错误自动修复')
	.option('--runtime-port <port>', '运行时监控端口', '3000')
	.option('--verbose', '显示详细信息')
	.option('--config <path>', '配置文件路径')
	.action(async (projectPath, options) => {
		const spinner = ora('初始化构建修复器...').start();

		try {
			// 确定项目路径
			const targetPath = projectPath ? path.resolve(projectPath) : process.cwd();

			// 验证项目路径
			if (!await fs.pathExists(targetPath)) {
				throw new Error(`项目路径不存在: ${targetPath}`);
			}

			// 验证是否为有效的 Node.js 项目
			const packageJsonPath = path.join(targetPath, 'package.json');
			if (!await fs.pathExists(packageJsonPath)) {
				throw new Error(`未找到 package.json 文件: ${packageJsonPath}`);
			}

			spinner.succeed('项目验证通过');

			// 加载配置
			const configLoader = new ConfigLoader();
			const config = await configLoader.loadConfig(options.config, targetPath);

			// 设置环境变量以控制详细输出
			if (options.verbose) {
				process.env.VERBOSE = 'true';
			}

			// 合并配置选项 - 命令行参数优先级最高
			const fixerOptions = {
				...config,
				// 只有在命令行明确指定时才覆盖配置文件的值
				...(options.buildCommand && { buildCommand: options.buildCommand }),
				...(options.devCommand && { devCommand: options.devCommand }),
				...(options.installCommand && { installCommand: options.installCommand }),
				...(options.maxAttempts && { maxAttempts: parseInt(options.maxAttempts) }),
				mode: options.mode || config.mode || 'build',
				...(options.devTimeout && { devTimeout: parseInt(options.devTimeout) }),
				useLegacyPeerDeps: options.legacyPeerDeps !== false && config.useLegacyPeerDeps !== false,
				...(options.skipInstall && { skipInstall: options.skipInstall }),
				...(options.skipAi && { skipAI: options.skipAi }),
				...(options.dryRun && { dryRun: options.dryRun }),
				...(options.interactive && { interactive: options.interactive }),
				...(options.explain && { explain: options.explain }),
				...(options.enableRuntimeRepair && { enableRuntimeRepair: options.enableRuntimeRepair }),
				...(options.runtimeTimeout && { runtimeTimeout: parseInt(options.runtimeTimeout) }),
				runtimeAutoFix: options.runtimeAutoFix !== false,
				...(options.runtimePort && { runtimePort: parseInt(options.runtimePort) }),
				...(options.verbose && { verbose: options.verbose })
			};

			console.log(chalk.blue('\n🔧 开始构建错误修复...'));
			console.log(chalk.gray(`📁 项目路径: ${targetPath}`));
			console.log(chalk.gray(`🎯 运行模式: ${fixerOptions.mode}`));
			if (fixerOptions.mode === 'dev') {
				console.log(chalk.gray(`🚀 Dev 命令: ${fixerOptions.devCommand}`));
				console.log(chalk.gray(`⏱️  超时时间: ${(fixerOptions.devTimeout || 30000) / 1000}s`));
			} else {
				console.log(chalk.gray(`🏗️  构建命令: ${fixerOptions.buildCommand}`));
			}
			console.log(chalk.gray(`🔄 最大尝试次数: ${fixerOptions.maxAttempts}`));
			console.log(chalk.gray(`🔧 运行时修复: ${fixerOptions.enableRuntimeRepair ? '启用' : '禁用'}`));

			if (fixerOptions.enableRuntimeRepair) {
				console.log(chalk.gray(`⏱️  运行时监控: ${fixerOptions.runtimeTimeout / 1000}s`));
				console.log(chalk.gray(`🌐 监控端口: ${fixerOptions.runtimePort}`));
				console.log(chalk.gray(`🤖 自动修复: ${fixerOptions.runtimeAutoFix ? '启用' : '禁用'}`));
			}

			if (fixerOptions.dryRun) {
				console.log(chalk.yellow('🔍 运行在预览模式，不会修改文件'));
			}

			const buildFixer = new BuildFixer(targetPath, fixerOptions);
			const result = await buildFixer.buildAndFix();
			displayResults(result, fixerOptions);
		} catch (error) {
			spinner.fail('构建修复失败');
			console.error(chalk.red('\n❌ 错误:'), error.message);

			if (options.verbose) {
				console.error(chalk.gray('\n详细错误信息:'));
				console.error(chalk.gray(error.stack));
			}

			process.exit(1);
		}
	});

program
	.command('config')
	.description('📋 显示配置信息')
	.option('--path <path>', '项目路径', process.cwd())
	.action(async (options) => {
		try {
			const targetPath = path.resolve(options.path);
			const configLoader = new ConfigLoader();
			const config = await configLoader.loadConfig(null, targetPath);

			console.log(chalk.blue('\n📋 当前配置:'));
			console.log(JSON.stringify(config, null, 2));
		} catch (error) {
			console.error(chalk.red('❌ 无法加载配置:'), error.message);
			process.exit(1);
		}
	});

program
	.command('init')
	.description('🚀 初始化配置文件')
	.option('--path <path>', '项目路径', process.cwd())
	.action(async (options) => {
		try {
			const targetPath = path.resolve(options.path);
			await initializeConfig(targetPath);
			console.log(chalk.green('✅ 配置文件初始化完成'));
		} catch (error) {
			console.error(chalk.red('❌ 初始化失败:'), error.message);
			process.exit(1);
		}
	});

async function initializeConfig(projectPath) {
	const configPath = path.join(projectPath, 'build-fixer.config.json');

	if (await fs.pathExists(configPath)) {
		console.log(chalk.yellow('⚠️  配置文件已存在，跳过初始化'));
		return;
	}

	const defaultConfig = {
		buildCommand: 'pnpm build',
		devCommand: 'pnpm dev',
		installCommand: 'pnpm install',
		maxAttempts: 6,
		mode: 'build',
		devTimeout: 30000,
		useLegacyPeerDeps: true,
		skipInstall: false,
		skipAI: false,
		dryRun: false,
		verbose: false,
		// 运行时错误修复配置
		enableRuntimeRepair: false,
		runtimeTimeout: 60000,
		runtimeAutoFix: true,
		runtimePort: 3000
	};

	await fs.writeJson(configPath, defaultConfig, { spaces: 2 });
	console.log(chalk.green(`✅ 配置文件已创建: ${configPath}`));
}

function displayResults(result, options) {
	console.log(chalk.blue('\n📊 修复结果总览:'));

	// 状态显示
	const statusIcon = result.success ? '🎉' : '⚠️';
	const statusColor = result.success ? chalk.green : chalk.yellow;
	console.log(`${statusIcon} 状态: ${statusColor(result.success ? '完全成功' : '部分成功')}`);

	// 统计信息
	console.log(chalk.gray('┌─ 统计信息'));
	console.log(chalk.gray(`├─ 构建尝试: ${result.attempts} 次`));
	console.log(chalk.gray(`├─ 修复错误: ${chalk.green(result.errorsFixed)} 个`));

	if (result.errorsSkipped > 0) {
		console.log(chalk.gray(`├─ 跳过错误: ${chalk.yellow(result.errorsSkipped)} 个`));
	}

	if (result.remainingErrors > 0) {
		console.log(chalk.gray(`├─ 剩余错误: ${chalk.red(result.remainingErrors)} 个`));
	}

	// 运行时错误修复统计
	if (result.runtimeRepair) {
		console.log(chalk.gray(`├─ 运行时修复: ${result.runtimeRepair.success ? chalk.green('成功') : chalk.yellow('失败')}`));
		if (result.runtimeErrorsDetected !== undefined) {
			console.log(chalk.gray(`├─ 运行时错误: ${result.runtimeErrorsDetected} 个`));
		}
		if (result.runtimeErrorsFixed !== undefined) {
			console.log(chalk.gray(`├─ 运行时修复: ${chalk.green(result.runtimeErrorsFixed)} 个`));
		}
	}

	if (result.duration) {
		const duration = Math.round(result.duration / 1000);
		console.log(chalk.gray(`└─ 总耗时: ${duration}s`));
	}

	// 详细原因
	if (result.reason) {
		console.log(chalk.gray(`\n📝 详细信息: ${result.reason}`));
	}

	// 成功情况的建议
	if (result.success) {
		console.log(chalk.green('\n🎉 恭喜！构建修复完全成功！'));
		console.log(chalk.blue('\n📋 后续建议:'));
		console.log(chalk.gray('  ✓ 运行测试确保功能正常'));
		console.log(chalk.gray('  ✓ 检查修复后的代码是否符合预期'));
		console.log(chalk.gray('  ✓ 提交代码变更'));
		console.log(chalk.gray('  ✓ 更新项目文档（如有必要）'));
	} else {
		// 失败情况的详细建议
		console.log(chalk.yellow('\n⚠️  构建修复未完全成功'));

		if (result.remainingErrors > 0) {
			console.log(chalk.blue('\n🔧 下一步操作建议:'));
			console.log(chalk.gray('  1. 查看剩余错误的详细信息'));
			console.log(chalk.gray('  2. 尝试手动修复复杂的错误'));
			console.log(chalk.gray('  3. 使用 --verbose 查看详细日志'));
			console.log(chalk.gray('  4. 使用 --interactive 选择性修复错误'));

			if (!options.skipAI) {
				console.log(chalk.gray('  5. 检查 AI 服务配置是否正确'));
			}
		}

		console.log(chalk.blue('\n💡 常见解决方案:'));
		console.log(chalk.gray('  • 检查依赖版本是否兼容 Vue 3'));
		console.log(chalk.gray('  • 参考 Vue 3 迁移指南'));
		console.log(chalk.gray('  • 查看项目的错误日志文件'));
		console.log(chalk.gray('  • 考虑分步骤进行迁移'));
	}

	console.log(chalk.blue('\n❓ 需要帮助?'));
	console.log(chalk.gray('  • 运行 build-fixer --help 查看所有选项'));
	console.log(chalk.gray('  • 使用 --explain 了解错误详情'));
	console.log(chalk.gray('  • 查看项目文档获取更多信息'));
}

process.on('uncaughtException', (error) => {
	console.error(chalk.red('❌ 未捕获的异常:'), error.message);
	process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
	console.error(chalk.red('❌ 未处理的 Promise 拒绝:'), reason);
	process.exit(1);
});

program.parse();

if (!process.argv.slice(2).length) {
	program.outputHelp();
}
