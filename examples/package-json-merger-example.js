#!/usr/bin/env node

const path = require('path');
const PackageJsonMerger = require('../src/features/package-json/PackageJsonMerger');

/**
 * PackageJsonMerger 使用示例
 * 演示如何在 Vue 2 到 Vue 3 迁移过程中智能合并 package.json
 */
async function runExample() {
  console.log('🚀 PackageJsonMerger 使用示例\n');

  // 示例项目路径
  const sourceProjectPath = path.join(__dirname, '../test/fixtures/test-vue2-project');
  const targetProjectPath = path.join(__dirname, '../test/fixtures/test-vue2-project-expect');

  try {
    // 创建 PackageJsonMerger 实例
    const merger = new PackageJsonMerger(sourceProjectPath, targetProjectPath, {
      preserveTargetDependencies: true,
      enableThirdPartyMapping: true,
      verbose: true,
      dryRun: true // 使用 dry run 模式，不实际写入文件
    });

    console.log('📦 开始合并 package.json...\n');

    // 执行合并
    const result = await merger.merge();

    if (result.success) {
      console.log('\n✅ 合并成功！\n');
      
      console.log('📋 变更摘要:');
      if (result.changes && result.changes.length > 0) {
        result.changes.forEach(change => {
          console.log(`  - ${change}`);
        });
      } else {
        console.log('  - 无变更');
      }

      console.log('\n📄 合并后的 package.json 依赖:');
      if (result.mergedPackageJson && result.mergedPackageJson.dependencies) {
        console.log('\n  dependencies:');
        Object.entries(result.mergedPackageJson.dependencies).forEach(([name, version]) => {
          console.log(`    ${name}: ${version}`);
        });
      }

      if (result.mergedPackageJson && result.mergedPackageJson.devDependencies) {
        console.log('\n  devDependencies:');
        Object.entries(result.mergedPackageJson.devDependencies).forEach(([name, version]) => {
          console.log(`    ${name}: ${version}`);
        });
      }

      // 特别展示 browserify polyfills
      console.log('\n🔧 自动添加的 Browserify Polyfills:');
      const deps = result.mergedPackageJson.dependencies || {};
      if (deps['path-browserify']) {
        console.log(`  ✓ path-browserify: ${deps['path-browserify']}`);
      }
      if (deps['stream-browserify']) {
        console.log(`  ✓ stream-browserify: ${deps['stream-browserify']}`);
      }

    } else {
      console.error('❌ 合并失败');
    }

  } catch (error) {
    console.error('❌ 执行过程中发生错误:', error.message);
    process.exit(1);
  }
}

// 运行示例
if (require.main === module) {
  runExample().catch(console.error);
}

module.exports = { runExample };
